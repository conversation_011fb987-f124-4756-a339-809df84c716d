package org.example.di

import kotlinx.coroutines.runBlocking
import org.example.addReceipt.ImageFileManager
import org.example.addReceipt.ImageFileManagerIos
import org.example.data.CategoriesTypesRoomImpl
import org.example.data.database.AppDatabase
import org.example.data.database.DatabaseInitializer
import org.example.data.database.createDatabase
import org.example.core.domain.repository.CategoriesTypesRepository
import org.koin.dsl.module

/**
 * iOS-specific Koin module for database dependencies
 */
val iosDatabaseModule = module {

    single<ImageFileManager> { ImageFileManagerIos() }

    // Provide Room database instance
    single<AppDatabase> { 
        val database = createDatabase()
        // Initialize database with default data
        runBlocking {
            DatabaseInitializer.initializeDatabase(database)
        }
        database
    }
    
    // Provide Room-based repository implementation
    single<CategoriesTypesRepository> {
        CategoriesTypesRoomImpl(get()) 
    }
}

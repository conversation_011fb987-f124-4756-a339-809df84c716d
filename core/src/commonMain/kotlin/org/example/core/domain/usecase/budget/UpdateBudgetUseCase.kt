package org.example.core.domain.usecase.budget

import org.example.core.domain.model.Budget
import org.example.core.domain.repository.BudgetRepository

/**
 * Use case for updating an existing budget
 */
class UpdateBudgetUseCase(
    private val budgetRepository: BudgetRepository
) {
    /**
     * Update an existing budget
     * @param budget The budget to update
     * @param changeReason Optional reason for the update
     */
    suspend operator fun invoke(budget: Budget, changeReason: String? = null): Result<Budget> {
        return try {
            val currentTime = kotlinx.datetime.Clock.System.now().toEpochMilliseconds()
            val updatedBudget = budget.copy(updatedAt = currentTime)
            
            val result = budgetRepository.updateBudget(updatedBudget, changeReason)
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

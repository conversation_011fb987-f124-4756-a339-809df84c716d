package org.example.core.domain.usecase.receipt

import org.example.core.domain.model.Receipt
import org.example.core.domain.repository.ReceiptRepository

/**
 * Use case for saving a receipt with its products
 */
class SaveReceiptUseCase(
    private val receiptRepository: ReceiptRepository
) {
    suspend operator fun invoke(receipt: Receipt): Result<Unit> /*TODO? return Receipt? for what?*/ {
        return try {
            receiptRepository.saveReceipt(receipt)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

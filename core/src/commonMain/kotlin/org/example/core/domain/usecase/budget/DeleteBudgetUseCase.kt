package org.example.core.domain.usecase.budget

import org.example.core.domain.repository.BudgetRepository

/**
 * Use case for deleting a budget
 */
class DeleteBudgetUseCase(
    private val budgetRepository: BudgetRepository
) {
    /**
     * Delete a budget
     * @param budgetId The ID of the budget to delete
     * @param changeReason Optional reason for deletion
     */
    suspend operator fun invoke(budgetId: Long, changeReason: String? = null): Result<Unit> {
        return try {
            budgetRepository.deleteBudget(budgetId, changeReason)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

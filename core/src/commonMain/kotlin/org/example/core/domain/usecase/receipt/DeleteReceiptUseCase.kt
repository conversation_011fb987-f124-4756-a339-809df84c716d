package org.example.core.domain.usecase.receipt

import org.example.core.domain.repository.ReceiptRepository

class DeleteReceiptUseCase(
    private val receiptRepository: ReceiptRepository
) {
    suspend operator fun invoke(receiptId: String): Result<Unit> {
        return try {
            receiptRepository.deleteReceipt(receiptId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
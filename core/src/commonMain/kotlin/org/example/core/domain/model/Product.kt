package org.example.core.domain.model


/**
 * Move @Immutable to ProductDisplayable model
 * import androidx.compose.runtime.Immutable
@Immutable*/
data class Product(
    val id: String,
    val name: String,
    val qty: String,
    val priceInCents: Long = 0,
    val totalInCents: Long = 0,
    val category: String,
    val type: String,
    val receiptId: String,
    val purchaseDate: String,
    val ocrGroupedTextLine: GroupedTextLine? = null
)
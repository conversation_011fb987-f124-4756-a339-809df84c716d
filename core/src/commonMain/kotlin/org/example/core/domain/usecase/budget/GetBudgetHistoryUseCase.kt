package org.example.core.domain.usecase.budget

import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDate
import org.example.core.domain.model.BudgetHistory
import org.example.core.domain.repository.BudgetRepository

/**
 * Use case for getting budget history
 */
class GetBudgetHistoryUseCase(
    private val budgetRepository: BudgetRepository
) {
    /**
     * Get budget history for a specific budget
     */
    fun getBudgetHistory(budgetId: Long): Flow<List<BudgetHistory>> {
        return budgetRepository.getBudgetHistory(budgetId)
    }
    
    /**
     * Get budget history for a specific week by start date
     */
    fun getBudgetHistoryForWeek(weekStartDate: LocalDate): Flow<List<BudgetHistory>> {
        return budgetRepository.getBudgetHistoryForWeek(weekStartDate)
    }
    
    /**
     * Get budget history for a specific week by year and week number
     */
    fun getBudgetHistoryForWeek(year: Int, weekNumber: Int): Flow<List<BudgetHistory>> {
        return budgetRepository.getBudgetHistoryForWeek(year, weekNumber)
    }
    
    /**
     * Get all budget history entries
     */
    fun getAllBudgetHistory(): Flow<List<BudgetHistory>> {
        return budgetRepository.getAllBudgetHistory()
    }
}

package org.example.core.domain.usecase.budget

import kotlinx.datetime.LocalDate
import org.example.core.domain.model.Budget
import org.example.core.domain.repository.BudgetRepository

/**
 * Use case for getting budget for a specific week
 */
class GetBudgetForWeekUseCase(
    private val budgetRepository: BudgetRepository
) {
    /**
     * Get budget for a specific week by start date
     */
    suspend operator fun invoke(weekStartDate: LocalDate): Result<Budget?> {
        return try {
            val budget = budgetRepository.getBudgetForWeek(weekStartDate)
            Result.success(budget)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get budget for a specific week by year and week number
     */
    suspend operator fun invoke(year: Int, weekNumber: Int): Result<Budget?> {
        return try {
            val budget = budgetRepository.getBudgetForWeek(year, weekNumber)
            Result.success(budget)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

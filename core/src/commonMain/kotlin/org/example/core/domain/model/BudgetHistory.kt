package org.example.core.domain.model

import kotlinx.datetime.LocalDate

/**
 * Domain model representing a budget change history entry
 */
data class BudgetHistory(
    val id: Long = 0,
    val budgetId: Long,
    val weekStartDate: LocalDate,
    val weekEndDate: LocalDate,
    val weekNumber: Int,
    val year: Int,
    val previousAmountInCents: Long?,
    val newAmountInCents: Long,
    val changeType: BudgetChangeType,
    val changeReason: String? = null,
    val createdAt: Long
) {
    /**
     * Get the amount difference in cents
     */
    fun getAmountDifference(): Long {
        return if (previousAmountInCents != null) {
            newAmountInCents - previousAmountInCents
        } else {
            newAmountInCents
        }
    }
    
    /**
     * Check if this was an increase
     */
    fun isIncrease(): Boolean {
        return previousAmountInCents != null && newAmountInCents > previousAmountInCents
    }
    
    /**
     * Check if this was a decrease
     */
    fun isDecrease(): Boolean {
        return previousAmountInCents != null && newAmountInCents < previousAmountInCents
    }
    
    /**
     * Get formatted change description
     */
    fun getChangeDescription(): String {
        return when (changeType) {
            BudgetChangeType.CREATED -> "Budżet utworzony"
            BudgetChangeType.UPDATED -> {
                if (previousAmountInCents == null) {
                    "Budżet utworzony"
                } else {
                    val difference = getAmountDifference()
                    when {
                        difference > 0 -> "Budżet zwiększony"
                        difference < 0 -> "Budżet zmniejszony"
                        else -> "Budżet zaktualizowany"
                    }
                }
            }
            BudgetChangeType.DELETED -> "Budżet usunięty"
        }
    }
}

/**
 * Enum representing the type of budget change
 */
enum class BudgetChangeType {
    CREATED,
    UPDATED,
    DELETED
}

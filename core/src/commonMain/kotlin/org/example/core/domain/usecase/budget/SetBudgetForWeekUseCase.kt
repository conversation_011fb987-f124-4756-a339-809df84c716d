package org.example.core.domain.usecase.budget

import kotlinx.datetime.LocalDate
import org.example.core.domain.model.Budget
import org.example.core.domain.repository.BudgetRepository

/**
 * Use case for setting budget for a specific week
 */
class SetBudgetForWeekUseCase(
    private val budgetRepository: BudgetRepository
) {
    /**
     * Set budget for a specific week
     * @param weekStartDate Start date of the week
     * @param weekEndDate End date of the week
     * @param weekNumber Week number in the year
     * @param year Year
     * @param budgetAmountInCents Budget amount in cents
     * @param changeReason Optional reason for setting the budget
     */
    suspend operator fun invoke(
        weekStartDate: LocalDate,
        weekEndDate: LocalDate,
        weekNumber: Int,
        year: Int,
        budgetAmountInCents: Long,
        changeReason: String? = null
    ): Result<Budget> {
        return try {
            val currentTime = kotlinx.datetime.Clock.System.now().toEpochMilliseconds()
            
            // Check if budget already exists for this week
            val existingBudget = budgetRepository.getBudgetForWeek(weekStartDate)
            
            val budget = if (existingBudget != null) {
                // Update existing budget
                val updatedBudget = existingBudget.copy(
                    budgetAmountInCents = budgetAmountInCents,
                    updatedAt = currentTime
                )
                budgetRepository.updateBudget(updatedBudget, changeReason)
            } else {
                // Create new budget
                val newBudget = Budget(
                    weekStartDate = weekStartDate,
                    weekEndDate = weekEndDate,
                    weekNumber = weekNumber,
                    year = year,
                    budgetAmountInCents = budgetAmountInCents,
                    createdAt = currentTime,
                    updatedAt = currentTime
                )
                budgetRepository.saveBudget(newBudget)
            }
            
            Result.success(budget)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

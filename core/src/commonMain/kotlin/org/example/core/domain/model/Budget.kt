package org.example.core.domain.model

import kotlinx.datetime.LocalDate
import kotlinx.datetime.toLocalDateTime

/**
 * Domain model representing a weekly budget
 */
data class Budget(
    val id: Long = 0,
    val weekStartDate: LocalDate,
    val weekEndDate: LocalDate,
    val weekNumber: Int,
    val year: Int,
    val budgetAmountInCents: Long,
    val createdAt: Long,
    val updatedAt: Long
) {
    /**
     * Get formatted date range for display
     */
    fun getDateRange(): String {
        return "${weekStartDate} - ${weekEndDate}"
    }
    
    /**
     * Check if this budget is for the current week
     */
    fun isCurrentWeek(): Boolean {
        val today = kotlinx.datetime.Clock.System.now().toLocalDateTime(kotlinx.datetime.TimeZone.currentSystemDefault()).date
        return today >= weekStartDate && today <= weekEndDate
    }
    
    /**
     * Get week identifier for comparison
     */
    fun getWeekIdentifier(): String {
        return "${year}-W${weekNumber.toString().padStart(2, '0')}"
    }
}

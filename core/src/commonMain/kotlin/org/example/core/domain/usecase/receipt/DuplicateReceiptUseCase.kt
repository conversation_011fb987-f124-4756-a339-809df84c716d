package org.example.core.domain.usecase.receipt

import org.example.core.domain.model.Receipt
import org.example.core.domain.repository.ReceiptRepository

/**
 * Use case for duplicating a receipt with its products. In the end it's just a save operation with new IDs for receipt and products assigned in the receipt manager.
 */
class DuplicateReceiptUseCase(
    private val receiptRepository: ReceiptRepository
) {
    suspend operator fun invoke(receipt: Receipt): Result<Receipt> {
        return try {
            val duplicatedReceipt = receiptRepository.duplicateReceipt(receipt)
            Result.success(duplicatedReceipt)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
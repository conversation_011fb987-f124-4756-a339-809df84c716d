package org.example.core.domain.repository

import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDate
import org.example.core.domain.model.Budget
import org.example.core.domain.model.BudgetHistory

/**
 * Repository interface for budget operations
 */
interface BudgetRepository {
    /**
     * Get budget for a specific week
     * @param weekStartDate The start date of the week
     * @return Budget for the week or null if not set*/

    suspend fun getBudgetForWeek(weekStartDate: LocalDate): Budget?
    
    /**
     * Get budget for a specific week by year and week number
     * @param year The year
     * @param weekNumber The week number
     * @return Budget for the week or null if not set*/

    suspend fun getBudgetForWeek(year: Int, weekNumber: Int): Budget?
    
    /**
     * Get all budgets as a flow
     * @return Flow of all budgets ordered by week start date*/

    fun getAllBudgets(): Flow<List<Budget>>
    
    /**
     * Get budgets for a specific year
     * @param year The year
     * @return Flow of budgets for the year*/

    fun getBudgetsForYear(year: Int): Flow<List<Budget>>
    
    /**
     * Save or update a budget
     * @param budget The budget to save
     * @return The saved budget with updated ID*/

    suspend fun saveBudget(budget: Budget): Budget
    
    /**
     * Update an existing budget
     * @param budget The budget to update
     * @param changeReason Optional reason for the change
     * @return The updated budget*/

    suspend fun updateBudget(budget: Budget, changeReason: String? = null): Budget
    
    /**
     * Delete a budget
     * @param budgetId The ID of the budget to delete
     * @param changeReason Optional reason for deletion*/

    suspend fun deleteBudget(budgetId: Long, changeReason: String? = null)
    
    /**
     * Get budget history for a specific budget
     * @param budgetId The budget ID
     * @return Flow of budget history entries*/

    fun getBudgetHistory(budgetId: Long): Flow<List<BudgetHistory>>
    
    /**
     * Get budget history for a specific week
     * @param weekStartDate The start date of the week
     * @return Flow of budget history entries for the week*/

    fun getBudgetHistoryForWeek(weekStartDate: LocalDate): Flow<List<BudgetHistory>>
    
   /**
     * Get budget history for a specific week by year and week number
     * @param year The year
     * @param weekNumber The week number
     * @return Flow of budget history entries for the week*/

    fun getBudgetHistoryForWeek(year: Int, weekNumber: Int): Flow<List<BudgetHistory>>
    
    /**
     * Get all budget history entries
     * @return Flow of all budget history entries ordered by creation date*/

    fun getAllBudgetHistory(): Flow<List<BudgetHistory>>
}

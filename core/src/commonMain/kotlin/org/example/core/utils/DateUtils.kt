package org.example.core.utils

import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atStartOfDayIn
import kotlinx.datetime.minus
import kotlinx.datetime.plus
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlin.time.ExperimentalTime

/**
 * Comprehensive date utilities for handling different date formats and conversions
 * throughout the application
 */
object DateUtils {

    /**
     * Get current timestamp in milliseconds
     */
    fun getCurrentTimestamp(): Long {
        return Clock.System.now().toEpochMilliseconds()
    }

    // MARK: - Timestamp, String, ISO  Conversions

    /**
     * Convert timestamp (milliseconds) to Instant
     */
    @OptIn(ExperimentalTime::class)
    fun timestampToInstant(timestamp: Long): Instant {
        return Instant.fromEpochMilliseconds(timestamp)
    }

    /**
     * * !!! This documentation may be outdated or incorrect !!!.
     * Convert ISO string date to timestamp (milliseconds)
     * Handles both formats:
     * - "2025-07-13T20:38:00" (from OpenAI API)
     * - "2025-07-13T20:38:00Z" (with timezone)
     */
    fun isoStringToTimestamp(
        isoString: String,
        timeZone: TimeZone = TimeZone.currentSystemDefault()
    ): Long {
        return try {
            val instant = if (isoString.endsWith("Z") ||
                isoString.contains("+") ||
                isoString.indexOf("-", startIndex = 10) != -1
            ) {
                // Has timezone info - parse as Instant
                Instant.parse(isoString)
            } else {
                // No timezone info - treat as local datetime
                val localDateTime = LocalDateTime.parse(isoString)
                localDateTime.toInstant(timeZone)
            }
            instant.toEpochMilliseconds()
        } catch (e: Exception) {
            println("Error parsing ISO string '$isoString': ${e.message}")
            getCurrentTimestamp() // Fallback to current time
        }
    }

    /**
     * Convert timestamp to ISO string format
     */
    fun timestampToIsoString(timestamp: Long): String {
        return timestampToInstant(timestamp).toString()
    }

    /**
     * Convert timestamp to DD.MM.YYYY format
     */
    fun timestampToDdMmYyyy(
        timestamp: Long,
        timeZone: TimeZone = TimeZone.currentSystemDefault()
    ): String {
        val localDateTime = timestampToInstant(timestamp).toLocalDateTime(timeZone)
        return "${
            localDateTime.dayOfMonth.toString().padStart(2, '0')
        }.${localDateTime.monthNumber.toString().padStart(2, '0')}.${localDateTime.year}"
    }

    /**
     * Convert timestamp to short date format (DD.MM)
     */
    fun timestampToShortDate(
        timestamp: Long,
        timeZone: TimeZone = TimeZone.currentSystemDefault()
    ): String {
        val localDateTime = timestampToInstant(timestamp).toLocalDateTime(timeZone)
        return "${
            localDateTime.dayOfMonth.toString().padStart(2, '0')
        }.${localDateTime.monthNumber.toString().padStart(2, '0')}"
    }

    /**
     * Convert LocalDate to timestamp (start of day)
     */
    fun localDateToTimestamp(
        localDate: LocalDate,
        timeZone: TimeZone = TimeZone.currentSystemDefault()
    ): Long {
        return localDate.atStartOfDayIn(timeZone).toEpochMilliseconds()
    }

    /**
     * Convert timestamp to LocalDate
     */
    fun timestampToLocalDate(
        timestamp: Long,
        timeZone: TimeZone = TimeZone.currentSystemDefault()
    ): LocalDate {
        return timestampToInstant(timestamp).toLocalDateTime(timeZone).date
    }

    /**
     * Convert timestamp to relative time string (Dzisiaj, Wczoraj, etc.)
     */
    fun timestampToRelativeTime(
        timestamp: Long,
        referenceTimestamp: Long = getCurrentTimestamp(),
        timeZone: TimeZone = TimeZone.currentSystemDefault()
    ): String {
        val targetInstant = timestampToInstant(timestamp)
        val referenceInstant = timestampToInstant(referenceTimestamp)

        val targetLocalDate = targetInstant.toLocalDateTime(timeZone)
        val referenceLocalDate = referenceInstant.toLocalDateTime(timeZone)

        val duration = referenceInstant - targetInstant
        val daysDifference = duration.inWholeDays

        // Check if dates are on the same calendar day
        val isSameDay = targetLocalDate.year == referenceLocalDate.year &&
                targetLocalDate.monthNumber == referenceLocalDate.monthNumber &&
                targetLocalDate.dayOfMonth == referenceLocalDate.dayOfMonth

        // Check if target date is yesterday
        val isYesterday = targetLocalDate.year == referenceLocalDate.year &&
                targetLocalDate.monthNumber == referenceLocalDate.monthNumber &&
                targetLocalDate.dayOfMonth == referenceLocalDate.dayOfMonth - 1

        // Check if target date is tomorrow
        val isTomorrow = targetLocalDate.year == referenceLocalDate.year &&
                targetLocalDate.monthNumber == referenceLocalDate.monthNumber &&
                targetLocalDate.dayOfMonth == referenceLocalDate.dayOfMonth + 1

        return when {
            isTomorrow -> "Jutro"
            daysDifference < -1 -> {
                val daysInFuture = -daysDifference
                "Za $daysInFuture dni"
            }

            isSameDay -> "Dzisiaj"
            isYesterday -> "Wczoraj"
            daysDifference > 1 -> "$daysDifference dni temu"
            else -> timestampToDdMmYyyy(timestamp, timeZone)
        }
    }

    // MARK: - Date Range Utilities

    /**
     * Get start of day timestamp
     */
    fun getStartOfDay(timestamp: Long, timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        val localDateTime = timestampToInstant(timestamp).toLocalDateTime(timeZone)
        val startOfDay = LocalDateTime(
            localDateTime.year, localDateTime.month, localDateTime.dayOfMonth, 0, 0, 0, 0
        )
        return startOfDay.toInstant(timeZone).toEpochMilliseconds()
    }

    /**
     * Get end of day timestamp
     */
    fun getEndOfDay(timestamp: Long, timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        val localDateTime = timestampToInstant(timestamp).toLocalDateTime(timeZone)
        val endOfDay = LocalDateTime(
            localDateTime.year,
            localDateTime.month,
            localDateTime.dayOfMonth,
            23,
            59,
            59,
            999_999_999
        )
        return endOfDay.toInstant(timeZone).toEpochMilliseconds()
    }

    /**
     * Get start of week (Monday) timestamp
     */
    fun getStartOfWeek(
        timestamp: Long,
        timeZone: TimeZone = TimeZone.currentSystemDefault()
    ): Long {
        val localDate = timestampToLocalDate(timestamp, timeZone)
        val dayOfWeek = localDate.dayOfWeek.ordinal // Monday = 0
        val startOfWeek = localDate.minus(dayOfWeek, DateTimeUnit.DAY)
        return localDateToTimestamp(startOfWeek, timeZone)
    }

    /**
     * Get end of week (Sunday) timestamp
     */
    fun getEndOfWeek(timestamp: Long, timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        val localDate = timestampToLocalDate(timestamp, timeZone)
        val dayOfWeek = localDate.dayOfWeek.ordinal // Monday = 0
        val endOfWeek = localDate.plus(6 - dayOfWeek, DateTimeUnit.DAY)
        return getEndOfDay(localDateToTimestamp(endOfWeek, timeZone), timeZone)
    }

    /**
     * Get start of month timestamp
     */
    fun getStartOfMonth(
        timestamp: Long,
        timeZone: TimeZone = TimeZone.currentSystemDefault()
    ): Long {
        val localDate = timestampToLocalDate(timestamp, timeZone)
        val startOfMonth = LocalDate(localDate.year, localDate.month, 1)
        return localDateToTimestamp(startOfMonth, timeZone)
    }

    /**
     * Get end of month timestamp
     */
    fun getEndOfMonth(timestamp: Long, timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        val localDate = timestampToLocalDate(timestamp, timeZone)
        val endOfMonth = LocalDate(localDate.year, localDate.month, getDaysInMonth(localDate))
        return getEndOfDay(localDateToTimestamp(endOfMonth, timeZone), timeZone)
    }

    /**
     * Get number of days in month for given date
     */
    fun getDaysInMonth(date: LocalDate): Int {
        val firstDayOfMonth = LocalDate(date.year, date.month, 1)
        val firstDayOfNextMonth = firstDayOfMonth.plus(1, DateTimeUnit.MONTH)
        val lastDayOfMonth = firstDayOfNextMonth.minus(1, DateTimeUnit.DAY)
        return lastDayOfMonth.dayOfMonth
    }

    // MARK: - Current Week Calendar Utilities

    /**
     * Polish day names (short form)
     */
    private val dayNamesCurrentWeek = listOf("nd", "pn", "wt", "śr", "cz", "pt", "sb")

    /**
     * Polish month names
     */
    private val monthNamesCurrentWeek = listOf(
        "Styczeń", "Luty", "Marzec", "Kwiecień", "Maj", "Czerwiec",
        "Lipiec", "Sierpień", "Wrzesień", "Październik", "Listopad", "Grudzień"
    )

    /**
     * Data class representing a day in the current week calendar
     */
    data class DayItemCurrentWeek(
        val day: String,
        val date: Int,
        val month: String,
        val isToday: Boolean = false,
        val isMonthStart: Boolean = false,
        val localDate: LocalDate
    )

    /**
     * Data class representing current week information
     */
    data class CurrentWeekInfoCurrentWeek(
        val weekNumber: Int,
        val days: List<DayItemCurrentWeek>
    )

    /**
     * Get current week information with all days and week number
     */
    fun getCurrentWeekInfoCurrentWeek(timeZone: TimeZone = TimeZone.currentSystemDefault()): CurrentWeekInfoCurrentWeek {
        val today = timestampToLocalDate(getCurrentTimestamp(), timeZone)

        // Get Monday of current week (kotlinx-datetime DayOfWeek: MONDAY = 1, SUNDAY = 7)
        val currentDayOfWeek = today.dayOfWeek.ordinal + 1 // Convert to 1-7 where Monday = 1
        val daysFromMonday =
            if (currentDayOfWeek == 7) 6 else currentDayOfWeek - 1 // Sunday = 6 days from Monday
        val monday = today.minus(daysFromMonday, DateTimeUnit.DAY)

        // Generate 7 days starting from Monday
        val weekDays = (0..6).map { dayOffset ->
            val currentDay = monday.plus(dayOffset, DateTimeUnit.DAY)
            val dayOfWeekIndex = (dayOffset + 1) % 7 // Convert to Sunday = 0, Monday = 1, etc.

            // Check if it's the start of a new month
            val isMonthStart = dayOffset == 0 ||
                    currentDay.monthNumber != monday.plus(
                dayOffset - 1,
                DateTimeUnit.DAY
            ).monthNumber

            DayItemCurrentWeek(
                day = dayNamesCurrentWeek[dayOfWeekIndex],
                date = currentDay.dayOfMonth,
                month = monthNamesCurrentWeek[currentDay.monthNumber - 1],
                isToday = currentDay == today,
                isMonthStart = isMonthStart,
                localDate = currentDay
            )
        }

        val weekNumber = getWeekNumberCurrentWeek(today)

        return CurrentWeekInfoCurrentWeek(
            weekNumber = weekNumber,
            days = weekDays
        )
    }

    /**
     * Calculate ISO week number for given date
     */
    fun getWeekNumberCurrentWeek(date: LocalDate): Int {
        // ISO week calculation
        val jan4 = LocalDate(date.year, 1, 4)
        val jan4DayOfWeek = jan4.dayOfWeek.ordinal // Monday = 0
        val firstMondayOfYear = jan4.minus(jan4DayOfWeek, DateTimeUnit.DAY)

        val daysDifference = date.toEpochDays() - firstMondayOfYear.toEpochDays()
        val weekNumber = (daysDifference / 7) + 1

        return weekNumber.toInt()
    }

    // MARK: - Time of Day Utilities

    /**
     * Get current time of day as fraction (0.0 - 1.0)
     * 0.0 = midnight, 0.5 = noon, 1.0 = end of day
     */
    fun getCurrentTimeOfDay(timeZone: TimeZone = TimeZone.currentSystemDefault()): Float {
        val now = timestampToInstant(getCurrentTimestamp()).toLocalDateTime(timeZone)
        val hourOfDay = now.hour
        val minuteOfHour = now.minute
        val secondOfMinute = now.second

        // Konwertuj na ułamek dnia (0.0 - 1.0)
        return (hourOfDay + minuteOfHour / 60f + secondOfMinute / 3600f) / 24f
    }

    // MARK: - Month Utilities

    /**
     * Get number of days in current month
     */
    fun getCurrentMonthDays(timeZone: TimeZone = TimeZone.currentSystemDefault()): Int {
        val today = timestampToLocalDate(getCurrentTimestamp(), timeZone)
        return getDaysInMonth(today)
    }

    /**
     * Get number of days in last month
     */
    fun getLastMonthDays(timeZone: TimeZone = TimeZone.currentSystemDefault()): Int {
        val today = timestampToLocalDate(getCurrentTimestamp(), timeZone)
        val lastMonth = today.minus(1, DateTimeUnit.MONTH)
        return getDaysInMonth(lastMonth)
    }


    /**
     * Get timestamp for start of yesterday
     */
    fun getYesterdayStart(timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        val today = timestampToLocalDate(getCurrentTimestamp(), timeZone)
        val yesterday = today.minus(1, DateTimeUnit.DAY)
        return localDateToTimestamp(yesterday, timeZone)
    }

    /**
     * Get timestamp for end of yesterday
     */
    fun getYesterdayEnd(timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        val yesterday = getYesterdayStart(timeZone)
        return getEndOfDay(yesterday, timeZone)
    }

    // MARK: - Validation Utilities

    /**
     * Check if two timestamps are on the same day
     */
    fun isSameDay(
        timestamp1: Long,
        timestamp2: Long,
        timeZone: TimeZone = TimeZone.currentSystemDefault()
    ): Boolean {
        val date1 = timestampToLocalDate(timestamp1, timeZone)
        val date2 = timestampToLocalDate(timestamp2, timeZone)
        return date1 == date2
    }

    /**
     * Check if timestamp range represents a single day
     */
    fun isSingleDayRange(
        startTimestamp: Long,
        endTimestamp: Long,
        timeZone: TimeZone = TimeZone.currentSystemDefault()
    ): Boolean {
        return isSameDay(startTimestamp, endTimestamp, timeZone)
    }
}

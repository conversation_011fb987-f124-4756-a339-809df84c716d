# 📘 README\_DEV\_ALTERNATIVE.md

## 🔄 Workflow – Model A (stałe branche modułowe)

W tym modelu zamiast zakładać nowe gałęzie per funkcjonalno<PERSON>ć (Model B), utrzymujemy **stałe gałęzie per moduł/feature**.

P<PERSON><PERSON>łady:

- `feat-dashboard`
- `feat-budget`
- `feat-addreceipt`

Każdy branch żyje dłużej i jest kontenerem dla rozwoju całego modułu.

---

## 📂 Jak pracować – krok po kroku

### 🔹 Start pracy nad modułem (np. dashboard)

```bash
git checkout main
git pull origin main   # pobierz najnowszy main
git checkout feat-dashboard  # przełącz się na stały branch modułu
git merge main   # dociągnij najnowsze zmiany z main
```

### 🔹 Dodawanie zmian

- Wszystkie nowe funkcjonalności dla **dashboardu** commitujesz \*\*na \*\*\`\`.
- Mo<PERSON><PERSON><PERSON> podzielić na commity: np. UI, CRUD, integracja.

```bash
git add .
git commit -m "Dashboard: dodanie nowego widgetu"
```

### 🔹 Wypchnięcie pracy

```bash
git push origin feat-dashboard
```

### 🔹 Włączenie do main

Gdy masz skończony większy etap (np. CRUD budżetu + integracja z dashboardem):

```bash
git checkout main
git pull origin main
git merge feat-dashboard
git push origin main
```

---

## 📊 Diagram – Model A

```
main ────────●────────●────────●───────────────●────────────
              \                                 ^
               \                               /
                ●────────●────────●────────●───
                feat-dashboard (żyje długo)

main ────────●─────────────●─────────────●───────────────●
               \                                       ^
                \                                     /
                 ●────●────●────●────●───────●─────────
                 feat-budget (też długowieczny)
```

👉 Każdy moduł rozwijasz w swojej gałęzi, okresowo mergując do `main`.

---

## 🔑 Cherry-pick – przenoszenie commitów

Czasem zdarzy się, że commit trafił nie na ten branch, co trzeba. Zamiast robić wszystko od nowa, możesz go **przenieść**:

1. Znajdź `SHA` commita:

```bash
git log --oneline
```

2. Przenieś commit na właściwy branch:

```bash
git checkout feat-dashboard
git cherry-pick <SHA>
```

➡️ Dzięki temu commit zostanie dodany do nowej gałęzi **zachowując autora i treść**.

---

## ✅ Zalety Modelu A

- Jeden branch = jeden moduł → łatwo skojarzyć zmiany.
- Mniej szumu w repozytorium (nie setki małych branchy).
- Wygodne przy **samodzielnej pracy** lub w małym zespole.
- Moduły mogą żyć długo i mieć własne tempo rozwoju.

## ⚠️ Wady Modelu A

- Historia brancha może być mniej czytelna (wiele różnych zmian w jednym worku).
- Cofnięcie tylko fragmentu pracy trudniejsze.
- Review w zespole bywa cięższe (duże branche = duże zmiany naraz).

---

## 🔎 Kiedy stosować Model A?

- Moduły są duże i rozwijane długo (np. dashboard przez kilka miesięcy).
- Pracujesz sam lub w małym teamie.
- Wolisz mieć jeden „kontener” dla całego feature, niż wiele drobnych gałęzi.

---

## 💡 Tipy praktyczne

- Rób **częste merge main → feat-modul**, żeby branch nie odbiegał zbyt mocno.
- Dziel zmiany w branchu na **małe commity** (UI / CRUD / integracja).
- Jeśli commit trafi na zły branch → użyj `cherry-pick`.
- Możesz **miksować Model A i B**: np. dla dashboardu (duży moduł) używać Model A, a dla receipts/budget (mniejsze taski) Model B.


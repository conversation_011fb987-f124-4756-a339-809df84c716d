package org.example.scanreceipt

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import org.example.addReceipt.scanner.AndroidDocumentScanner
import org.example.addReceipt.scanner.ReceiptImageManager

class MainActivity : ComponentActivity() {
    private lateinit var documentScannerLauncher: ActivityResultLauncher<IntentSenderRequest>
    private lateinit var imageManager: ReceiptImageManager

    override fun onCreate(savedInstanceState: Bundle?) {
        installSplashScreen()
        enableEdgeToEdge()
        super.onCreate(savedInstanceState)


        imageManager = ReceiptImageManager(context = this)
        imageManager.cleanupOldTempFiles()

        documentScannerLauncher = registerForActivityResult(
            ActivityResultContracts.StartIntentSenderForResult()
        ) { result ->
            AndroidDocumentScanner.handleActivityResult(result)
        }

        AndroidDocumentScanner.setLauncher(documentScannerLauncher)
        AndroidDocumentScanner.setAppContext(this)

        setContent {
            App()
        }
    }
}

@Preview
@Composable
fun AppAndroidPreview() {
    App()
}
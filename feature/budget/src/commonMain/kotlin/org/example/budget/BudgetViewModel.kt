package org.example.budget

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.datetime.LocalDate
import org.example.core.domain.usecase.budget.DeleteBudgetUseCase
import org.example.core.domain.usecase.budget.GetBudgetForWeekUseCase
import org.example.core.domain.usecase.budget.GetBudgetHistoryUseCase
import org.example.core.domain.usecase.budget.SetBudgetForWeekUseCase
import org.example.core.domain.usecase.budget.UpdateBudgetUseCase

class BudgetViewModel(
    private val getBudgetForWeekUseCase: GetBudgetForWeekUseCase,
    private val setBudgetForWeekUseCase: SetBudgetForWeekUseCase,
    private val updateBudgetUseCase: UpdateBudgetUseCase,
    private val deleteBudgetUseCase: DeleteBudgetUseCase,
    private val getBudgetHistoryUseCase: GetBudgetHistoryUseCase
) : ViewModel() {

    private val _uiState = MutableStateFlow(BudgetUiState())
    val uiState: StateFlow<BudgetUiState> = _uiState.asStateFlow()

    private var currentWeekData: WeekData? = null
    private var lastFailedEvent: BudgetEvent? = null

    fun onEvent(event: BudgetEvent) {
        when (event) {
            is BudgetEvent.LoadBudgetForWeek -> loadBudgetForWeek(event)
            is BudgetEvent.StartEditingBudget -> startEditingBudget()
            is BudgetEvent.CancelEditingBudget -> cancelEditingBudget()
            is BudgetEvent.UpdateBudgetInput -> updateBudgetInput(event.input)
            is BudgetEvent.SaveBudget -> saveBudget(event.changeReason)
            is BudgetEvent.DeleteBudget -> deleteBudget(event.changeReason)
            is BudgetEvent.LoadBudgetHistory -> loadBudgetHistory()
            is BudgetEvent.ClearError -> clearError()
            is BudgetEvent.ClearSuccess -> clearSuccess()
            is BudgetEvent.Retry -> retryLastFailedOperation()
        }
    }

    private fun loadBudgetForWeek(event: BudgetEvent.LoadBudgetForWeek) {
        currentWeekData = WeekData(
            weekNumber = event.weekNumber,
            startDate = event.weekStartDate,
            endDate = event.weekEndDate,
            dateRange = "${event.weekStartDate} - ${event.weekEndDate}"
        )

        _uiState.update { it.copy(isLoading = true, errorMessage = null) }

        viewModelScope.launch {
            getBudgetForWeekUseCase(event.weekStartDate)
                .onSuccess { budget ->
                    _uiState.update { currentState ->
                        currentState.copy(
                            isLoading = false,
                            selectedWeekBudget = budget,
                            budgetInputText = budget?.let {
                                formatPrice(it.budgetAmountInCents)
                            } ?: ""
                        )
                    }
                    // Load history after loading budget
                    loadBudgetHistory()
                }
                .onFailure { error ->
                    lastFailedEvent = event
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            errorMessage = "Błąd podczas ładowania budżetu: ${error.message}"
                        )
                    }
                }
        }
    }

    private fun startEditingBudget() {
        _uiState.update { it.copy(isEditingBudget = true, errorMessage = null) }
    }

    private fun cancelEditingBudget() {
        val currentBudget = _uiState.value.selectedWeekBudget
        _uiState.update {
            it.copy(
                isEditingBudget = false,
                budgetInputText = currentBudget?.let { budget ->
                    formatPrice(budget.budgetAmountInCents)
                    /*String.format("%.2f", budget.budgetAmountInCents / 100.0)*/
                } ?: "",
                errorMessage = null
            )
        }
    }

    private fun updateBudgetInput(input: String) {
        _uiState.update { it.copy(budgetInputText = input, errorMessage = null) }
    }

    private fun saveBudget(changeReason: String?) {
        val currentState = _uiState.value
        val weekData = currentWeekData ?: return

        if (!currentState.isInputValid()) {
            _uiState.update { it.copy(errorMessage = "Wprowadź poprawną kwotę budżetu") }
            return
        }

        val budgetAmountInCents = currentState.getBudgetAmountInCentsFromInput() ?: return

        _uiState.update { it.copy(isLoading = true, errorMessage = null) }

        viewModelScope.launch {
            setBudgetForWeekUseCase(
                weekStartDate = weekData.startDate,
                weekEndDate = weekData.endDate,
                weekNumber = weekData.weekNumber,
                year = weekData.startDate.year,
                budgetAmountInCents = budgetAmountInCents,
                changeReason = changeReason
            )
                .onSuccess { budget ->
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            selectedWeekBudget = budget,
                            isEditingBudget = false,
                            isSuccessMessageVisible = true,
                            successMessage = "Budżet został zapisany pomyślnie"
                        )
                    }
                    // Reload history after saving
                    loadBudgetHistory()
                }
                .onFailure { error ->
                    lastFailedEvent = BudgetEvent.SaveBudget(changeReason)
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            errorMessage = "Błąd podczas zapisywania budżetu: ${error.message}"
                        )
                    }
                }
        }
    }

    private fun deleteBudget(changeReason: String?) {
        val currentBudget = _uiState.value.selectedWeekBudget ?: return

        _uiState.update { it.copy(isLoading = true, errorMessage = null) }

        viewModelScope.launch {
            deleteBudgetUseCase(currentBudget.id, changeReason)
                .onSuccess {
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            selectedWeekBudget = null,
                            budgetInputText = "",
                            isEditingBudget = false,
                            isSuccessMessageVisible = true,
                            successMessage = "Budżet został usunięty"
                        )
                    }
                    // Reload history after deletion
                    loadBudgetHistory()
                }
                .onFailure { error ->
                    lastFailedEvent = BudgetEvent.DeleteBudget(changeReason)
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            errorMessage = "Błąd podczas usuwania budżetu: ${error.message}"
                        )
                    }
                }
        }
    }

    private fun loadBudgetHistory() {
        val weekData = currentWeekData ?: return

        viewModelScope.launch {
            getBudgetHistoryUseCase.getBudgetHistoryForWeek(
                weekData.startDate.year,
                weekData.weekNumber
            ).collect { history ->
                _uiState.update { it.copy(budgetHistory = history) }
            }
        }
    }

    private fun clearError() {
        _uiState.update { it.copy(errorMessage = null) }
    }

    private fun clearSuccess() {
        _uiState.update { it.copy(isSuccessMessageVisible = false, successMessage = null) }
    }

    private fun retryLastFailedOperation() {
        lastFailedEvent?.let { event ->
            lastFailedEvent = null
            onEvent(event)
        }
    }
}

fun formatPrice(priceInCents: Long, separator: String = ","): String {
    val baseUnit = priceInCents / 100
    val subunit = priceInCents % 100
    return "$baseUnit$separator${subunit.toString().padStart(2, '0')}"
}
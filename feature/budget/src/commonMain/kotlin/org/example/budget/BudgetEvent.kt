package org.example.budget

import kotlinx.datetime.LocalDate

/**
 * Events for the Budget screen
 */
sealed class BudgetEvent {
    
    /**
     * Load budget for a specific week
     */
    data class LoadBudgetForWeek(
        val weekStartDate: LocalDate,
        val weekEndDate: LocalDate,
        val weekNumber: Int,
        val year: Int
    ) : BudgetEvent()
    
    /**
     * Start editing budget
     */
    data object StartEditingBudget : BudgetEvent()
    
    /**
     * Cancel editing budget
     */
    data object CancelEditingBudget : BudgetEvent()
    
    /**
     * Update budget input text
     */
    data class UpdateBudgetInput(val input: String) : BudgetEvent()
    
    /**
     * Save budget for the selected week
     */
    data class SaveBudget(val changeReason: String? = null) : BudgetEvent()
    
    /**
     * Delete budget for the selected week
     */
    data class DeleteBudget(val changeReason: String? = null) : BudgetEvent()
    
    /**
     * Load budget history for the selected week
     */
    data object LoadBudgetHistory : BudgetEvent()
    
    /**
     * Clear error message
     */
    data object ClearError : BudgetEvent()
    
    /**
     * Clear success message
     */
    data object ClearSuccess : BudgetEvent()
    
    /**
     * Retry last failed operation
     */
    data object Retry : BudgetEvent()
}

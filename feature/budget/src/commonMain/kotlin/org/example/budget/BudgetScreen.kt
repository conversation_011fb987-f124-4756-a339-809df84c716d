package org.example.budget

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBack
import androidx.compose.material.icons.rounded.Circle
import androidx.compose.material.icons.rounded.Delete
import androidx.compose.material.icons.rounded.Edit
import androidx.compose.material.icons.rounded.Today
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.DayOfWeek
import kotlinx.datetime.LocalDate
import kotlinx.datetime.plus
import org.example.core.utils.DateUtils
import org.koin.compose.viewmodel.koinViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BudgetScreen(
    navigateBack: () -> Unit,
    viewModel: BudgetViewModel = koinViewModel()
) {
    val currentWeekIndex = remember { getCurrentWeekIndex() }
    var selectedWeekIndex by remember { mutableIntStateOf(currentWeekIndex) }
    val listState = rememberLazyListState(initialFirstVisibleItemIndex = selectedWeekIndex)

    // Generate weeks for current year
    val weeks = remember { generateWeeksForCurrentYear() }

    // Collect UI state
    val uiState by viewModel.uiState.collectAsState()

    val scope = rememberCoroutineScope()

    // Load budget for initially selected week
    LaunchedEffect(selectedWeekIndex) {
        val selectedWeek = weeks[selectedWeekIndex]
        viewModel.onEvent(
            BudgetEvent.LoadBudgetForWeek(
                weekStartDate = selectedWeek.startDate,
                weekEndDate = selectedWeek.endDate,
                weekNumber = selectedWeek.weekNumber,
                year = selectedWeek.startDate.year
            )
        )
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(text = "Budżet")
                },
                navigationIcon = {
                    IconButton(onClick = navigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Rounded.ArrowBack,
                            contentDescription = "Powrót"
                        )
                    }
                },
                actions = {
                    if (selectedWeekIndex != currentWeekIndex) {
                        IconButton(
                            onClick = {
                                selectedWeekIndex = currentWeekIndex
                                scope.launch {
                                    listState.animateScrollToItem(currentWeekIndex)
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Rounded.Today,
                                contentDescription = "Obecny tydzień"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Week selector
            LazyRow(
                state = listState,
                contentPadding = PaddingValues(horizontal = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.padding(vertical = 16.dp)
            ) {
                itemsIndexed(weeks) { index, week ->
                    WeekItem(
                        week = week,
                        isSelected = index == selectedWeekIndex,
                        isCurrentWeek = index == currentWeekIndex,
                        onClick = {
                            selectedWeekIndex = index
                            // Load budget for newly selected week will be triggered by LaunchedEffect
                        }
                    )
                }
            }

            Divider()

            // Main budget content
            BudgetContent(
                selectedWeek = weeks[selectedWeekIndex],
                uiState = uiState,
                onEvent = viewModel::onEvent,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            )
        }
    }
}

@Composable
private fun WeekItem(
    week: WeekData,
    isSelected: Boolean,
    isCurrentWeek: Boolean,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.width(120.dp),
        colors = CardDefaults.cardColors(
            containerColor = when {
                isSelected -> MaterialTheme.colorScheme.primaryContainer
                isCurrentWeek -> MaterialTheme.colorScheme.tertiaryContainer
                else -> MaterialTheme.colorScheme.surface
            }
        ),
        border = if (isSelected) {
            BorderStroke(2.dp, MaterialTheme.colorScheme.primary)
        } else null
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "Tydzień ${week.weekNumber}",
                    style = MaterialTheme.typography.labelMedium,
                    color = when {
                        isSelected -> MaterialTheme.colorScheme.onPrimaryContainer
                        isCurrentWeek -> MaterialTheme.colorScheme.onTertiaryContainer
                        else -> MaterialTheme.colorScheme.onSurface
                    }
                )
                if (isCurrentWeek && !isSelected) {
                    Spacer(modifier = Modifier.width(4.dp))
                    Icon(
                        imageVector = Icons.Rounded.Circle,
                        contentDescription = "Obecny tydzień",
                        modifier = Modifier.size(6.dp),
                        tint = MaterialTheme.colorScheme.onTertiaryContainer
                    )
                }
            }
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = week.dateRange,
                style = MaterialTheme.typography.bodySmall,
                color = when {
                    isSelected -> MaterialTheme.colorScheme.onPrimaryContainer
                    isCurrentWeek -> MaterialTheme.colorScheme.onTertiaryContainer
                    else -> MaterialTheme.colorScheme.onSurfaceVariant
                },
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun BudgetContent(
    selectedWeek: WeekData,
    uiState: BudgetUiState,
    onEvent: (BudgetEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.verticalScroll(rememberScrollState())
    ) {
        // Week info header
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.secondaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Tydzień ${selectedWeek.weekNumber}",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
                Text(
                    text = selectedWeek.dateRange,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Budget section
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Budżet tygodniowy",
                        style = MaterialTheme.typography.titleMedium
                    )

                    if (uiState.isBudgetSet() && !uiState.isEditingBudget) {
                        Row {
                            IconButton(
                                onClick = { onEvent(BudgetEvent.StartEditingBudget) }
                            ) {
                                Icon(
                                    imageVector = Icons.Rounded.Edit,
                                    contentDescription = "Edytuj budżet"
                                )
                            }
                            IconButton(
                                onClick = { onEvent(BudgetEvent.DeleteBudget()) }
                            ) {
                                Icon(
                                    imageVector = Icons.Rounded.Delete,
                                    contentDescription = "Usuń budżet",
                                    tint = MaterialTheme.colorScheme.error
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                if (uiState.isEditingBudget) {
                    // Budget input field
                    OutlinedTextField(
                        value = uiState.budgetInputText,
                        onValueChange = { onEvent(BudgetEvent.UpdateBudgetInput(it)) },
                        label = { Text("Kwota budżetu (zł)") },
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Decimal,
                            imeAction = ImeAction.Done
                        ),
                        keyboardActions = KeyboardActions(
                            onDone = {
                                if (uiState.isInputValid()) {
                                    onEvent(BudgetEvent.SaveBudget())
                                }
                            }
                        ),
                        isError = uiState.errorMessage != null,
                        supportingText = uiState.errorMessage?.let { { Text(it) } },
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Action buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        OutlinedButton(
                            onClick = { onEvent(BudgetEvent.CancelEditingBudget) },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("Anuluj")
                        }
                        Button(
                            onClick = { onEvent(BudgetEvent.SaveBudget()) },
                            enabled = uiState.isInputValid() && !uiState.isLoading,
                            modifier = Modifier.weight(1f)
                        ) {
                            if (uiState.isLoading) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(16.dp),
                                    strokeWidth = 2.dp
                                )
                            } else {
                                Text("Zapisz")
                            }
                        }
                    }
                } else {
                    // Current budget display
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Obecny budżet:",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = uiState.getFormattedBudgetAmount(),
                            style = MaterialTheme.typography.titleMedium,
                            color = if (uiState.isBudgetSet())
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = if (uiState.selectedWeekBudget != null) {
                            val lastUpdate = DateUtils.timestampToRelativeTime(uiState.selectedWeekBudget.updatedAt)
                            "Ostatnia zmiana: $lastUpdate"
                        } else {
                            "Nie ustawiono"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    if (!uiState.isBudgetSet()) {
                        Spacer(modifier = Modifier.height(16.dp))

                        // Set budget button
                        Button(
                            onClick = { onEvent(BudgetEvent.StartEditingBudget) },
                            modifier = Modifier.fillMaxWidth(),
                            enabled = !uiState.isLoading
                        ) {
                            if (uiState.isLoading) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(16.dp),
                                    strokeWidth = 2.dp
                                )
                            } else {
                                Text("Ustaw budżet")
                            }
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // History section
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Historia zmian",
                    style = MaterialTheme.typography.titleMedium
                )

                Spacer(modifier = Modifier.height(12.dp))

                if (uiState.budgetHistory.isEmpty()) {
                    Text(
                        text = "Brak zmian w tym tygodniu",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth().padding(vertical = 16.dp)
                    )
                } else {
                    LazyColumn(
                        modifier = Modifier.heightIn(max = 300.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(uiState.budgetHistory) { historyEntry ->
                            BudgetHistoryItem(historyEntry = historyEntry)
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun BudgetHistoryItem(
    historyEntry: org.example.core.domain.model.BudgetHistory,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = historyEntry.getChangeDescription(),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = DateUtils.timestampToRelativeTime(historyEntry.createdAt),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                val previousAmount = historyEntry.previousAmountInCents
                if (previousAmount != null) {
                    Text(
                        text = "${formatPrice(previousAmount)} zł → ${formatPrice(historyEntry.newAmountInCents)} zł",
                        style = MaterialTheme.typography.bodySmall
                    )
                } else {
                    Text(
                        text = "${formatPrice(historyEntry.newAmountInCents)} zł",
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                val amountDifference = historyEntry.getAmountDifference()
                if (previousAmount != null && amountDifference != 0L) {
                    Text(
                        text = if (amountDifference > 0) "+${ formatPrice(amountDifference)} zł"
                               else "${formatPrice(amountDifference)} zł",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (amountDifference > 0)
                            MaterialTheme.colorScheme.primary
                        else
                            MaterialTheme.colorScheme.error
                    )
                }
            }

            historyEntry.changeReason?.let { reason ->
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "Powód: $reason",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

// Helper functions

private fun generateWeeksForCurrentYear(): List<WeekData> {
    val today = DateUtils.timestampToLocalDate(DateUtils.getCurrentTimestamp())
    val currentYear = today.year
    val weeks = mutableListOf<WeekData>()

    // Start from first Monday of the year
    var date = LocalDate(currentYear, 1, 1)
    while (date.dayOfWeek != DayOfWeek.MONDAY) {
        date = date.plus(1, DateTimeUnit.DAY)
    }

    var weekNumber = 1
    while (date.year == currentYear) {
        val startOfWeek = date
        val endOfWeek = date.plus(6, DateTimeUnit.DAY)

        val dateRange = "${DateUtils.timestampToShortDate(DateUtils.localDateToTimestamp(startOfWeek))} - " +
                "${DateUtils.timestampToShortDate(DateUtils.localDateToTimestamp(endOfWeek))}"

        weeks.add(
            WeekData(
                weekNumber = weekNumber,
                startDate = startOfWeek,
                endDate = endOfWeek,
                dateRange = dateRange
            )
        )

        date = date.plus(7, DateTimeUnit.DAY)
        weekNumber++

        // Stop if we've moved to next year
        if (date.year != currentYear) break
    }

    return weeks
}

private fun getCurrentWeekIndex(): Int {
    val today = DateUtils.timestampToLocalDate(DateUtils.getCurrentTimestamp())
    val weeks = generateWeeksForCurrentYear()

    return weeks.indexOfFirst { week ->
        today >= week.startDate && today <= week.endDate
    }.takeIf { it >= 0 } ?: 0
}
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBack
import androidx.compose.material.icons.filled.AddCircle
import androidx.compose.material.icons.rounded.Clear
import androidx.compose.material.icons.rounded.CloudUpload
import androidx.compose.material.icons.rounded.CopyAll
import androidx.compose.material.icons.rounded.Delete
import androidx.compose.material.icons.rounded.DensityLarge
import androidx.compose.material.icons.rounded.DensityMedium
import androidx.compose.material.icons.rounded.MoreVert
import androidx.compose.material.icons.rounded.Save
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SuggestionChip
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.example.addReceipt.AddReceiptEvent
import org.example.addReceipt.AddReceiptUiState
import org.example.addReceipt.CheckForRecoveredScan
import org.example.addReceipt.DeleteState
import org.example.addReceipt.OpenAiState
import org.example.addReceipt.SaveState
import org.example.addReceipt.component.DateTimePickerDialog
import org.example.addReceipt.component.OverlayMode
import org.example.addReceipt.component.ProductCard
import org.example.addReceipt.component.ReceiptImageSection
import org.example.addReceipt.component.SwipeToDeleteContainer
import org.example.addReceipt.formatPrice
import org.example.addReceipt.scanner.rememberDocumentScanner
import org.example.addReceipt.scanner.rememberTextRecognizer
import org.example.core.domain.model.GroupedTextLine


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddReceiptContent(
    uiState: AddReceiptUiState,
    onEvent: (AddReceiptEvent) -> Unit,
    scope: CoroutineScope,
    navigateBack: () -> Unit = {},
    startWithScan: Boolean = false
) {
    val documentScanner = rememberDocumentScanner()
    val textRecognizer = rememberTextRecognizer()
    val startScan = uiState.startScan
//    val scanRecovered = uiState.scanRecovered
    val scanAlreadyStarted = uiState.scanAlreadyStarted

    // Check for recovered scan on startup (Android only)
    CheckForRecoveredScan(onEvent, textRecognizer, onResult = {

    })
    LaunchedEffect(startWithScan) {
        if (startWithScan && !scanAlreadyStarted) {
            onEvent(AddReceiptEvent.StartScan)
        }
    }

    // Auto-start scanning if requested
    LaunchedEffect(startScan, scanAlreadyStarted) {
        if (startScan && !scanAlreadyStarted) {
            println("AddReceiptContent: Starting document scan...")
            // Mark scan as started to prevent re-triggering
            onEvent(AddReceiptEvent.OmitStartScan)
            val result = documentScanner.scanDocument()
            if (result != null) {
                onEvent(AddReceiptEvent.UpdateReceiptImage(result))
                onEvent(AddReceiptEvent.UpdateOcrResult(null))
                val ocrResult = textRecognizer.recognizeTextWithDetails(result)
                onEvent(AddReceiptEvent.UpdateOcrResult(ocrResult))
            }
        }
    }

    LaunchedEffect(uiState.saveState, uiState.deleteState) {
        when {
            uiState.saveState is SaveState.Success -> {
                println("AddReceiptContent LaunchedEffect: Navigate back from launched effect")
                navigateBack()
            }

            uiState.deleteState is DeleteState.Success -> {
                println("AddReceiptContent LaunchedEffect: Navigate back from launched effect")
                navigateBack()
            }
        }
    }

    // Stan dla podświetlonej linii OCR
    var highlightedProductLine by remember { mutableStateOf<GroupedTextLine?>(null) } // TODO wywalic to do AddReceiptUiState?

    // State for confirmation dialog
    var showClearAllInputsConfirmation by remember { mutableStateOf(false) }
    var showDeleteConfirmation by remember { mutableStateOf(false) }

    // bottom action bar
    var menuExpanded by remember { mutableStateOf(false) }

    // Stan dla globalnego expand/collapse
    var expandAll by remember { mutableStateOf(true) }

    // Mapa stanów dla poszczególnych produktów
    var expandedStates by remember {
        mutableStateOf(
            uiState.products.associate { it.id to true }.toMutableMap()
        )
    }

//    val ocrResult = uiState.ocrResult
//    ocrResult?.let { result ->
//        println("=========== OCR RESULT ===========")
//        println("📄 ocrResult.text:\n${result.groupedWordsIntoLines}")
//        println("----------------------------------")

//        println("🔹 MyTextBlocks:")
//        result.myTextBlocks.forEachIndexed { i, block ->
//            println("- [$i] text: \"${block.text}\"")
////            println("      bbox: ${block.myBoundingBox}") TEGO NIE POCZEBA DO TRAINING DATA
//            println("      confidence: ${(block.confidence).toString().take(4)}")
//        }

//        println("🔸 Grouped Lines:")
//        result.groupedLinesWithDetails.forEachIndexed { i, line ->
//            println("- [$i] line: \"${line.text}\" line boundingBox: ${line.myBoundingBox}")
//            line.elements.forEach { element ->
//                println(
//                    "   • word: \"${element.text}\" (conf: ${
//                        (element.confidence).toString().take(4)
//                    })"
//                )
//            }
//        }
//
//        println("==================================")
//    } ?: run {
//        println("⚠️  ocrResult is null")
//    }
    LaunchedEffect(uiState.products) {
        val currentIds = uiState.products.map { it.id }.toSet()
        expandedStates = expandedStates.filterKeys { it in currentIds }.toMutableMap()

        // Dodaj nowe produkty z domyślnym stanem
        uiState.products.forEach { product ->
            if (product.id !in expandedStates) {
                expandedStates[product.id] = expandAll
            }
        }
    }

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = if (uiState.isEditMode) "Edytuj paragon" else "Dodaj paragon",
                        style = MaterialTheme.typography.titleLarge
                    )
                },
                navigationIcon = {
                    IconButton(onClick = navigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Rounded.ArrowBack,
                            contentDescription = "Powrót"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.background
                )
            )
        },
        bottomBar = {
        },
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Receipt Image Section
            var overlayMode by remember { mutableStateOf(OverlayMode.LINES) }
            ReceiptImageSection(
                uiState = uiState,
                imagePath = uiState.imagePath,
                ocrResult = uiState.ocrResult,
                highlightedLine = highlightedProductLine,
                onCameraClick = {
                    scope.launch {
                        // Reset scan flag to allow manual scanning
                        onEvent(AddReceiptEvent.ResetScanFlag)
                        val result = documentScanner.scanDocument()
//                        println("result: $result")
                        if (result != null) {
                            onEvent(AddReceiptEvent.UpdateReceiptImage(result))
                            // Start text recognition
                            onEvent(AddReceiptEvent.UpdateOcrResult(null)) // Set to loading state
                            highlightedProductLine = null
//                            scope.launch {
                            val ocrResult = textRecognizer.recognizeTextWithDetails(result)
                            onEvent(AddReceiptEvent.UpdateOcrResult(ocrResult))
//                            }
                        }
                    }
                },
                onImageDeleteClick = {
                    onEvent(AddReceiptEvent.DeleteImage)
                    highlightedProductLine = null
                    overlayMode = OverlayMode.NONE
                },
                onRetryOcr = {
                    // Retry OCR by re-processing the current image
                    uiState.imagePath?.let { imagePath ->
                        scope.launch {
                            onEvent(AddReceiptEvent.UpdateOcrResult(null)) // Set to loading state
                            val ocrResult = textRecognizer.recognizeTextWithDetails(imagePath)
                            onEvent(AddReceiptEvent.UpdateOcrResult(ocrResult))
                        }
                    }
                },
                onRetryOpenAi = {
                    // Retry OpenAI processing
                    onEvent(AddReceiptEvent.RetryOpenAi)
                },
                onRetryExport = {
                    // Retry export
                    onEvent(AddReceiptEvent.ShareReceiptCsv)
                },
                onRetrySave = {
                    // Retry save
                    onEvent(AddReceiptEvent.SaveReceipt)
                },
                onClearSuccessState = {
                    println("ReceiptImageSection onClearSuccessState invoked onEvent(AddReceiptEvent.ClearSuccessStates)")
                    // Clear success states
                    onEvent(AddReceiptEvent.ClearSuccessStates)
                }
            )

            Spacer(modifier = Modifier.height(8.dp))

            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(24.dp))
                    .background(Color.White)
                    .padding(/*top = 8.dp,*/ start = 8.dp, end = 8.dp),
//                contentPadding = PaddingValues(bottom = 16.dp)
            ) {
                item {
                    // Receipt Details Section
                    Spacer(modifier = Modifier.height(8.dp))
                    ReceiptDetails(
                        storeName = uiState.storeName,
                        storeAddress = uiState.storeAddress,
                        receiptSum = formatPrice(uiState.receiptSumInCents),
                        purchaseMethod = uiState.purchaseMethod,
                        purchaseDate = uiState.purchaseDate,
                        onNameChange = { onEvent(AddReceiptEvent.UpdateStoreName(it)) },
                        onShopAddressChange = { onEvent(AddReceiptEvent.UpdateAddress(it)) },
                        onReceiptSumChange = { onEvent(AddReceiptEvent.UpdateReceiptSum(it)) },
                        onPurchaseMethodChange = { onEvent(AddReceiptEvent.UpdatePurchaseMethod(it)) },
                        onDateClick = {
                            onEvent(AddReceiptEvent.ShowDateTimePicker)
                        },
                        enableTypewriterEffect = uiState.openAiState is OpenAiState.Success,
                    )
                }
                item {
                    Spacer(modifier = Modifier.height(16.dp))
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        )
                        {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(16.dp)
                            ) {
                                Text(
                                    "Produkty",
                                    style = MaterialTheme.typography.titleLarge,
                                    color = Color.Black.copy(alpha = 0.8f)
                                )
                                SuggestionChip(
                                    onClick = { /*TODO*/ },
                                    label = {
                                        AnimatedContent(
                                            targetState = uiState.products.size,
                                            transitionSpec = {
                                                if (targetState > initialState) {
                                                    // If the target count is larger, it slides up and fades in
                                                    // while the initial (smaller) one slides up and fades out.
                                                    (slideInVertically { height -> height } + fadeIn()).togetherWith(
                                                        slideOutVertically { height -> -height } + fadeOut())
                                                } else {
                                                    // If the target count is smaller, it slides down and fades out
                                                    // while the new (larger) one slides down and fades in.
                                                    (slideInVertically { height -> -height } + fadeIn()).togetherWith(
                                                        slideOutVertically { height -> height } + fadeOut())
                                                }
                                            }
                                        ) {
                                            Text("${uiState.products.size}")
                                        }
                                    }
                                )
                            }
                            Row() {
                                IconButton(
                                    onClick = {
                                        expandAll = !expandAll
                                        // Zaktualizuj wszystkie karty
                                        expandedStates =
                                            expandedStates.mapValues { expandAll }.toMutableMap()
                                    }
                                ) {
                                    AnimatedContent(
                                        targetState = expandAll,
                                        transitionSpec = {
                                            (slideInVertically { height -> height } + fadeIn()).togetherWith(
                                                slideOutVertically { height -> -height } + fadeOut())

                                        }
                                    ) {
                                        if (expandAll) {
                                            Icon(
                                                imageVector = Icons.Rounded.DensityLarge,
                                                contentDescription = "Expand all products"
                                            )
                                        } else {
                                            Icon(
                                                imageVector = Icons.Rounded.DensityMedium,
                                                contentDescription = "Collapse all products"
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                items(uiState.products, key = { it.id }) { product ->
                    SwipeToDeleteContainer(
                        item = product,
                        onDelete = {
                            onEvent(AddReceiptEvent.DeleteProduct(product))
                        },
                    ) { productItem ->
                        ProductCard(
                            product = productItem,
                            name = productItem.name,
                            onNameChange = { newName ->
                                onEvent(AddReceiptEvent.UpdateProductName(productItem.id, newName))
                            },
                            qty = productItem.qty,
                            onQtyChange = { newQty ->
                                onEvent(AddReceiptEvent.UpdateProductQty(productItem.id, newQty))
                            },
                            price = formatPrice(productItem.priceInCents),
                            onPriceInCentsChange = { newPriceInCents ->
                                onEvent(
                                    AddReceiptEvent.UpdateProductPrice(
                                        productItem.id,
                                        newPriceInCents
                                    )
                                )
                            },
                            total = formatPrice(productItem.totalInCents),
                            onTotalInCentsChange = { newTotalInCents ->
                                onEvent(
                                    AddReceiptEvent.UpdateProductTotal(
                                        productItem.id,
                                        newTotalInCents
                                    )
                                )
                            },
                            categories = uiState.categories,
                            onCategorySelected = { newCategory ->
                                onEvent(
                                    AddReceiptEvent.UpdateProductCategory(
                                        productItem.id,
                                        newCategory
                                    )
                                )
                            },
                            types = uiState.types,
                            onTypeSelected = { newType ->
                                onEvent(AddReceiptEvent.UpdateProductType(productItem.id, newType))
                            },
                            onFocus = { focusedLine: GroupedTextLine? ->
                                highlightedProductLine = focusedLine
                            },
                            isExpanded = expandedStates[productItem.id] ?: true,
                            onExpandedChange = { expanded ->
                                expandedStates = expandedStates.toMutableMap().apply {
                                    this[productItem.id] = expanded
                                }
                                expandAll = expandedStates.values.all { it }
                            }
                        )
                    }
                    Spacer(modifier = Modifier.height(8.dp))
                }

                item {
                    AddProductButton(
                        onAddProduct = { onEvent(AddReceiptEvent.AddNewProduct) }
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                }
                item {
                    // Bottom Action Row
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // More options menu
                        Box {
                            IconButton(
                                onClick = { menuExpanded = true }
                            ) {
                                Icon(
                                    imageVector = Icons.Rounded.MoreVert,
                                    contentDescription = "More options"
                                )
                            }

                            DropdownMenu(
                                expanded = menuExpanded,
                                onDismissRequest = { menuExpanded = false }
                            ) {
                                DropdownMenuItem(
                                    text = { Text("Udostępnij CSV") },
                                    onClick = {
                                        menuExpanded = false
                                        onEvent(AddReceiptEvent.ShareReceiptCsv)
                                    },
                                    enabled = !uiState.isAnyOperationInProgress,
                                    leadingIcon = {
                                        Icon(
                                            imageVector = Icons.Rounded.CloudUpload,
                                            contentDescription = "Share CSV",
                                        )
                                    }
                                )
                                DropdownMenuItem(
                                    text = { Text("Zduplikuj") },
                                    onClick = {
                                        menuExpanded = false
                                        onEvent(AddReceiptEvent.DuplicateReceipt)
                                    },
                                    enabled = !uiState.isAnyOperationInProgress,
                                    leadingIcon = {
                                        Icon(
                                            imageVector = Icons.Rounded.CopyAll,
                                            contentDescription = "Duplicate receipt",
                                        )
                                    }
                                )
                                DropdownMenuItem(
                                    text = { Text("Wyczyść wprowadzone dane") },
                                    onClick = {
                                        menuExpanded = false
                                        showClearAllInputsConfirmation = true
                                    },
                                    enabled = !uiState.isAnyOperationInProgress,
                                    leadingIcon = {
                                        Icon(
                                            imageVector = Icons.Rounded.Clear,
                                            contentDescription = "Clear all inputs",
                                        )
                                    }
                                )
                                DropdownMenuItem(
                                    text = { Text("Usuń paragon") },
                                    onClick = {
                                        menuExpanded = false
                                        showDeleteConfirmation = true
                                    },
                                    enabled = !uiState.isAnyOperationInProgress,
                                    leadingIcon = {
                                        Icon(
                                            imageVector = Icons.Rounded.Delete,
                                            contentDescription = "Clear all inputs",
                                        )
                                    }
                                )
                            }
                        }

                        // Save button
                        Button(
                            onClick = {
//                                TODO Nawigowanie w buttonie nie dziala bo sprawdzenie SaveState wykonuje sie zbyt szybko a operacja save jest asynchroniczna
//                                println("AddReceiptContent.saveButton.onClick: Initiating save")
//                                println("AddReceiptContent.saveButton.onClick: uiState.saveState=${uiState.saveState}")
                                onEvent(AddReceiptEvent.SaveReceipt)
//                                if (uiState.saveState is SaveState.Success) {
//                                    println("AddReceiptContent.saveButton.onClick: uiState.saveState=${uiState.saveState}")
//                                    println("AddReceiptContent: Navigate back")
//                                    navigateBack()
//                                } else {
//                                    println("AddReceiptContent.saveButton.onClick: uiState.saveState=${uiState.saveState}")
//                                }
                            },
                            enabled = !uiState.isAnyOperationInProgress
                        ) {
                            Icon(
                                imageVector = Icons.Rounded.Save,
                                contentDescription = if (uiState.isEditMode) "Update receipt" else "Save receipt"
                            )
                            Spacer(modifier = Modifier.padding(4.dp))
                            Text(if (uiState.isEditMode) "Aktualizuj" else "Zapisz")
                        }
                    }
                    Spacer(modifier = Modifier.height(16.dp))
                }
            }

        }
    }

    // DateTime Picker Dialog
    if (uiState.showDateTimePicker) {
        println("purchaseDate on datePickerOpen: ${uiState.purchaseDate}")
        DateTimePickerDialog(
            initialDateTime = uiState.purchaseDate,
            onDateTimeSelected = { dateTime ->
                onEvent(AddReceiptEvent.UpdatePurchaseDate(dateTime))
                onEvent(AddReceiptEvent.HideDateTimePicker)
            },
            onDismiss = {
                onEvent(AddReceiptEvent.HideDateTimePicker)
            }
        )
    }

    // Delete Confirmation Dialog
    if (showClearAllInputsConfirmation) {
        AlertDialog(
            onDismissRequest = { showClearAllInputsConfirmation = false },
            title = { Text("Potwierdź wyczyszczenie") },
            text = { Text("Czy na pewno chcesz wyczyścić wszystkie dane? Ta operacja nie może być cofnięta.") },
            confirmButton = {
                TextButton(
                    onClick = {
                        showClearAllInputsConfirmation = false
                        onEvent(AddReceiptEvent.ClearAllInputs)
                        highlightedProductLine = null
                    }
                ) {
                    Text("Usuń")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showClearAllInputsConfirmation = false }
                ) {
                    Text("Anuluj")
                }
            }
        )
    }
    if (showDeleteConfirmation) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirmation = false },
            title = { Text("Potwierdź usunięcie") },
            text = { Text("Czy na pewno chcesz USUNĄĆ ten paragon? Ta operacja nie może być cofnięta.") },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDeleteConfirmation = false
                        onEvent(AddReceiptEvent.DeleteReceipt)
                        highlightedProductLine = null
                    }
                ) {
                    Text("Usuń")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteConfirmation = false }
                ) {
                    Text("Anuluj")
                }
            }
        )
    }
}

@Composable
private fun AddProductButton(onAddProduct: () -> Unit) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onAddProduct() },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.7f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Icon(
                modifier = Modifier.size(24.dp),
                imageVector = Icons.Default.AddCircle,
                contentDescription = "Add new product to list icon",
                tint = Color(0xFF6B9EFF)
            )

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                "Dodaj produkt",
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF6B9EFF)
            )
        }
    }
}
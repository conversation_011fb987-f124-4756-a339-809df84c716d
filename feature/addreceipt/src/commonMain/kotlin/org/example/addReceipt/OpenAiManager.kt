package org.example.addReceipt

import org.example.core.domain.exception.APIException
import org.example.core.domain.exception.NetworkException
import org.example.core.domain.exception.ParseReceiptException
import org.example.core.domain.exception.TimeoutException
import org.example.core.domain.model.GroupedTextLine
import org.example.core.domain.model.ai.ParsedReceiptData
import org.example.core.domain.usecase.ai.ParseReceiptUseCase

class OpenAiManager(
    private val stateManager: UiStateManager,
    private val parseReceiptUseCase: ParseReceiptUseCase
) {

    fun updateOpenAiState(newState: OpenAiState) {
        stateManager.updateState { it.copy(openAiState = newState) }
    }

    fun resetToIdle() {
        updateOpenAiState(OpenAiState.Idle)
    }

    suspend fun parseReceiptText(
        ocrText: String,
        originalOcrLines: List<GroupedTextLine>
    ): Result<Unit> {
        return try {
            updateOpenAiState(OpenAiState.Processing)
            stateManager.clearError()

            parseReceiptUseCase(ocrText)
                .onSuccess { parsedData ->
                    handleParsingSuccess(parsedData, originalOcrLines)
                }
                .onFailure { error ->
                    val (errorMessage, errorType) = categorizeError(error)
                    updateOpenAiState(
                        OpenAiState.Error(
                            message = errorMessage,
                            errorType = errorType
                        )
                    )
                    return Result.failure(error)
                }

            Result.success(Unit)
        } catch (e: Exception) {
            val (errorMessage, errorType) = categorizeError(e)
            updateOpenAiState(
                OpenAiState.Error(
                    message = errorMessage,
                    errorType = errorType
                )
            )
            Result.failure(e)
        }
    }

    private fun handleParsingSuccess(
        parsedData: ParsedReceiptData,
        originalOcrLines: List<GroupedTextLine>
    ) {
        val productUiItems = createProductUiItemsFromParsedData(parsedData, originalOcrLines)
        stateManager.updateState { currentState ->
            currentState.copy(
                storeName = parsedData.storeName,
                storeAddress = parsedData.storeAddress,
                purchaseDate = parseOpenAiDateTime(parsedData.purchaseDateTime ?: ""),
                receiptSumInCents = (parsedData.receiptSum * 100).toLong(),
                purchaseMethod = parsedData.purchaseMethod ?: "",
                products = productUiItems.ifEmpty { listOf(ProductDisplayable()) },
                openAiState = OpenAiState.Success,
            )
        }
    }

    private fun createProductUiItemsFromParsedData(
        parsedData: ParsedReceiptData,
        originalOcrLines: List<GroupedTextLine>
    ): List<ProductDisplayable> {
        return parsedData.products.mapNotNull { parsedProduct ->
            val productNameLower = parsedProduct.name.lowercase().trim()

            if (productNameLower.isBlank()) {
                return@mapNotNull null
            }

            val matchingOcrLine = originalOcrLines.find { ocrLine ->
                val ocrLineTextLower = ocrLine.text.lowercase().trim()
                ocrLineTextLower.contains(productNameLower)
            }

            ProductDisplayable(
                name = parsedProduct.name,
                qty = parsedProduct.quantity.toString(),
                priceInCents = (parsedProduct.unitPrice * 100).toLong(),
                totalInCents = (parsedProduct.totalPrice * 100).toLong(),
                category = parsedProduct.category,
                type = parsedProduct.type,
                totalNotBlank = (parsedProduct.totalPrice * 100).toLong() > 0L,
                ocrGroupedTextLine = matchingOcrLine
            )
        }
    }

    private fun categorizeError(exception: Throwable): Pair<String, OpenAiErrorType> {
        return when (exception) {
            is TimeoutException -> {
                "Przekroczono czas oczekiwania. Sprawdź połączenie internetowe i spróbuj ponownie." to OpenAiErrorType.TIMEOUT_ERROR
            }
            is NetworkException -> {
                "Błąd połączenia. Sprawdź dostęp do internetu i spróbuj ponownie." to OpenAiErrorType.NETWORK_ERROR
            }
            is APIException -> {
                "Błąd serwera OpenAI. Spróbuj ponownie za chwilę." to OpenAiErrorType.API_ERROR
            }
            is ParseReceiptException -> {
                "Nie udało się przetworzyć danych z paragonu. Spróbuj ponownie lub wprowadź dane ręcznie." to OpenAiErrorType.PARSING_ERROR
            }
            else -> {
                "Wystąpił nieoczekiwany błąd: ${exception.message}" to OpenAiErrorType.UNKNOWN_ERROR
            }
        }
    }
}
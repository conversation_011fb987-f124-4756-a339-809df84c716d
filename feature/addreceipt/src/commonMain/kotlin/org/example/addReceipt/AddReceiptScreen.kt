package org.example.addReceipt

import AddReceiptContent
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun AddReceiptScreen(
    receiptId: String? = null,
    navigateBack: () -> Unit = {},
    startWithScan: Boolean = false
) {
    val viewModel = koinViewModel<AddReceiptViewModel>()
    val uiState by viewModel.uiState.collectAsState()
    val scope = rememberCoroutineScope()

    // Load receipt for editing if receiptId is provided
    DisposableEffect(receiptId) {
        if (receiptId != null) {
            viewModel.onEvent(AddReceiptEvent.LoadReceipt(receiptId))
        }
        onDispose { }
    }

    AddReceiptContent(
        uiState = uiState,
        onEvent = viewModel::onEvent,
        scope = scope,
        navigateBack = navigateBack,
        startWithScan = startWithScan
    )
}
package org.example.addReceipt.component.Image

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.dp
import org.example.core.domain.model.MyTextBlock

@Composable
fun BlockOverlay(
    myTextBlocks: List<MyTextBlock>,
    imageWidth: Int,
    imageHeight: Int,
    displayWidth: Float,
    displayHeight: Float,
    modifier: Modifier = Modifier
) {
    Canvas(modifier = modifier.fillMaxSize()) {
        val scaleX = size.width / imageWidth
        val scaleY = size.height / imageHeight

        myTextBlocks.forEach { textBlock ->
            val rect = textBlock.myBoundingBox

            // Skaluj współrzędne bounding box do rozmiaru wyświetlanego obrazu
            val left = rect.left * scaleX
            val top = rect.top * scaleY
            val right = rect.right * scaleX
            val bottom = rect.bottom * scaleY

            // Narysuj prostokąt wokół tekstu
            drawRect(
                color = androidx.compose.ui.graphics.Color.Red,
                topLeft = Offset(left, top),
                size = Size(right - left, bottom - top),
                style = Stroke(width = 2.dp.toPx())
            )

            // Opcjonalnie: narysuj tło pod tekstem
            drawRect(
                color = androidx.compose.ui.graphics.Color.Red.copy(alpha = 0.2f),
                topLeft = Offset(left, top),
                size = Size(right - left, bottom - top)
            )
        }
    }
}
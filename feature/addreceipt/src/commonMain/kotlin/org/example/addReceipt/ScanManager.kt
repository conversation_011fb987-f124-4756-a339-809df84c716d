package org.example.addR<PERSON>eipt

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.example.addReceipt.scanner.DocumentScanner
import org.example.addReceipt.scanner.TextRecognizerML

/**
 * Manages document scanning logic and state transitions
 * Encapsulates scanning business logic following Clean Architecture principles
 */
class ScanManager(
    private val stateManager: UiStateManager,
) {
    
    /**
     * Requests automatic scanning (triggered by startWithScan parameter)
     */
    fun requestAutoScan() {
        val currentState = stateManager.getCurrentState()
        if (currentState.scanState == ScanState.Idle) {
            stateManager.updateState { it.copy(scanState = ScanState.PendingAutoScan) }
        }
    }
    
    /**
     * Requests manual scanning (triggered by camera button)
     */
    fun requestManualScan() {
        stateManager.updateState { it.copy(scanState = ScanState.PendingAutoScan) }
    }
    
    /**
     * Marks scan as started to prevent re-triggering
     */
    fun markScanStarted() {
        stateManager.updateState { it.copy(scanState = ScanState.Scanning) }
    }
    
    /**
     * Marks scan as completed (prevents auto-scan re-triggering after process death)
     */
    fun markScanCompleted() {
        stateManager.updateState { it.copy(scanState = ScanState.Completed) }
    }
    
    /**
     * Resets scan state to allow new scanning
     */
    fun resetScanState() {
        stateManager.updateState { it.copy(scanState = ScanState.Idle) }
    }
    
    /**
     * Checks if auto-scan should be triggered
     */
    fun shouldTriggerAutoScan(): Boolean {
        return stateManager.getCurrentState().scanState == ScanState.PendingAutoScan
    }
    
    /**
     * Executes the scanning process with proper state management
     */
    suspend fun executeScan(
        documentScanner: DocumentScanner,
        textRecognizer: TextRecognizerML,
        scope: CoroutineScope,
        onEvent: (AddReceiptEvent) -> Unit
    ) {
        markScanStarted()
        
        try {
            val result = documentScanner.scanDocument()
            if (result != null) {
                // Update image and start OCR
                onEvent(AddReceiptEvent.UpdateReceiptImage(result))
                onEvent(AddReceiptEvent.UpdateOcrResult(null))
                
                scope.launch {
                    val ocrResult = textRecognizer.recognizeTextWithDetails(result)
                    onEvent(AddReceiptEvent.UpdateOcrResult(ocrResult))
                }
                
                markScanCompleted()
            } else {
                // Scan was cancelled, reset to idle
                resetScanState()
            }
        } catch (e: Exception) {
            println("ScanManager: Error during scan: ${e.message}")
            resetScanState()
        }
    }
    
    /**
     * Handles scan recovery after process death
     */
    fun handleScanRecovery(imagePath: String, onEvent: (AddReceiptEvent) -> Unit) {
        // Mark as completed to prevent auto-scan re-triggering
        markScanCompleted()
        
        // Process the recovered image
        onEvent(AddReceiptEvent.UpdateReceiptImage(imagePath))
    }
}

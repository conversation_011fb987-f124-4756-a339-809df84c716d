package org.example.addReceipt

import org.example.core.domain.model.Product
import org.example.core.domain.model.Receipt
import org.example.core.domain.usecase.receipt.DeleteReceiptUseCase
import org.example.core.domain.usecase.receipt.DuplicateReceiptUseCase
import org.example.core.domain.usecase.receipt.SaveReceiptUseCase
import org.example.core.domain.usecase.receipt.UpdateReceiptUseCase
import org.example.core.domain.usecase.receipt.GetReceiptsUseCase
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid


class ReceiptManager(
    private val saveReceiptUseCase: SaveReceiptUseCase,
    private val duplicateReceiptUseCase: DuplicateReceiptUseCase,
    private val updateReceiptUseCase: UpdateReceiptUseCase,
    private val getReceiptsUseCase: GetReceiptsUseCase,
    private val deleteReceiptUseCase: DeleteReceiptUseCase,
    private val imageFileManager: ImageFileManager? = null
) {
    suspend fun loadReceipt(receiptId: String): Result<Receipt> {
        return try {
            val receipt = getReceiptsUseCase.getReceiptById(receiptId)
            if (receipt != null) {
                Result.success(receipt)
            } else {
                // todo co tak naprawde moze spowodowac receipt == null? bledne id?
                Result.failure(Exception("Receipt not found. Receipt = null"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save receipt and products. Return kotlin Result and updating stateManager
     * Deciding if it's a new receipt or an update based on stateManager.isEditMode
     * This metod is also responsible for:
     *  - Moving temporary image file to final location
     *  - Deleting temporary file
     *  This method utilizes methods:
     *  - createReceiptFromState
     *  - handleImageFileForSaving
     *  - saveReceiptUseCase
     *  - updateReceiptUseCase
     *  This metod is no longer responsible for updating UI state.
     */
    suspend fun saveReceipt(currentState: AddReceiptUiState): Result<Receipt> {
        return try {
            val receipt = createReceiptFromState(currentState)

            // Handle image file management
            val finalReceipt = handleImageFileForSaving(receipt, currentState)

            val result = if (currentState.isEditMode && currentState.editingReceiptId != null) {
                updateReceiptUseCase(finalReceipt)
            } else {
                saveReceiptUseCase(finalReceipt)
            }

            if (result.isSuccess) {
                Result.success(finalReceipt) //TODO zmienic usecase, repository aby zwracalo Receipt - wtedy bede mial id i np bede mogl nawigowac do ReceiptOverview czy tam details
            } else {
                val exception = result.exceptionOrNull()
                return Result.failure(exception ?: Exception("Unknown error"))
            }
//            stateManager.clearState() //TODO to bylo przyczyna nie nawigowania do tylu. zanim dostalem result w viewModel to status byl zresetowany do idle
        } catch (e: Exception) {
            Result.failure(e)
        }
    }


    @OptIn(ExperimentalUuidApi::class)
    private fun createReceiptFromState(state: AddReceiptUiState): Receipt {
        val receiptId = if (state.isEditMode && state.editingReceiptId != null) {
            state.editingReceiptId
        } else {
            Uuid.random().toHexString()
        }

        return Receipt(
            id = receiptId,
            name = state.storeName,
            products = state.products.map { productDisplayable ->
                Product(
                    id = productDisplayable.id,
                    name = productDisplayable.name,
                    qty = productDisplayable.qty,
                    priceInCents = productDisplayable.priceInCents,
                    totalInCents = productDisplayable.totalInCents,
                    category = productDisplayable.category,
                    type = productDisplayable.type,
                    purchaseDate = state.purchaseDate,
                    receiptId = receiptId,
                    ocrGroupedTextLine = productDisplayable.ocrGroupedTextLine
                )
            },
            saveDate = getCurrentTimeString(),
            purchaseDate = state.purchaseDate,
            receiptSum = state.receiptSumInCents,
            purchaseMethod = state.purchaseMethod,
            productIds = state.products.map { it.id },
            imagePath = state.imagePath
        )
    }

    private fun handleImageFileForSaving(
        receipt: Receipt,
        currentState: AddReceiptUiState
    ): Receipt {
        val imagePath = currentState.imagePath

        // If no image or no image manager, return as is
        if (imagePath == null || imageFileManager == null) {
//            println("ReceiptManager: No image path or image manager, keeping original receipt")
            return receipt
        }

        // Check if this is a temporary file that needs to be moved
        if (imagePath.contains("temp_scan_")) {
//            println("ReceiptManager: Moving temporary file to final location")
//            println("ReceiptManager: Temp file: $imagePath")
//            println("ReceiptManager: Receipt ID: ${receipt.id}")

            val finalPath = imageFileManager.moveToFinalLocation(imagePath, receipt.id)
            if (finalPath != null) {
//                println("ReceiptManager: Successfully moved file to: $finalPath")
                return receipt.copy(imagePath = finalPath)
            } else {
                println("ReceiptManager: Failed to move file, keeping original path")
                return receipt
            }
        } else {
            println("ReceiptManager: Image path is not temporary, keeping as is: $imagePath")
            return receipt
        }
    }

    @OptIn(ExperimentalUuidApi::class)
    suspend fun duplicateReceipt(currentState: AddReceiptUiState): Result<Receipt> {
        return try {
            val receipt = createReceiptFromState(currentState)
            val newReceiptId = Uuid.random().toHexString()
            val newProducts = receipt.products
            val result: Result<Receipt> = duplicateReceiptUseCase(
                receipt.copy(
                    id = newReceiptId,
                    name = "Kopia - ${receipt.name}",
                    imagePath = "",
                    purchaseDate = getCurrentTimeString(),
                    saveDate = getCurrentTimeString(),
                    products = newProducts.map {
                        it.copy(
                            id = Uuid.random().toHexString(),
                            receiptId = newReceiptId
                        )
                    }
                )
            )

            if (result.isSuccess) {
                Result.success(result.getOrNull()!!) //TODO czy jest pewne ze skoro jest success to getOrNull() nie bedzie null?
            } else {
                val exception = result.exceptionOrNull()
                return Result.failure(exception ?: Exception("Unknown error"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun deleteReceipt(receiptId: String): Result<Unit> {
        return try {
            val result = if (receiptId.isNotEmpty()) {
                deleteReceiptUseCase(receiptId)
            } else {
                Result.failure(Exception("Receipt ID is empty"))
            }

            if (result.isSuccess) {
                return Result.success(Unit)
            } else {
                val exception = result.exceptionOrNull()
                return Result.failure(exception ?: Exception("Unknown error"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
package org.example.addReceipt

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update

interface StateManager<T> {
    fun updateState(transform: (T) -> T)
    fun getCurrentState(): T
}

class UiStateManager(
    private val _uiState: MutableStateFlow<AddReceiptUiState>
) : StateManager<AddReceiptUiState> {

    override fun updateState(transform: (AddReceiptUiState) -> AddReceiptUiState) {
        _uiState.update(transform)
    }

    override fun getCurrentState(): AddReceiptUiState = _uiState.value

    fun clearError() {
        updateState { it.copy(uiError = null) }
    }

    fun setError(error: UiError) {
        updateState { it.copy(uiError = error) }
    }

    fun clearState() {
        updateState {
            AddReceiptUiState().copy(
                categories = it.categories,
                types = it.types,
                scanAlreadyStarted = false // Reset scan flag when clearing state
            )
        }
    }
}
package org.example.addReceipt

import org.example.addReceipt.scanner.OcrResult
import org.example.core.domain.model.GroupedTextLine

class OcrManager(private val stateManager: UiStateManager) {

    fun updateReceiptImage(imagePath: String?) {
        stateManager.updateState { currentState ->
            currentState.copy(
                imagePath = imagePath,
                ocrState = if (imagePath != null) OcrState.Processing else OcrState.Idle,
                ocrResult = null,
                scanAlreadyStarted = if (imagePath == null) false else currentState.scanAlreadyStarted
//                /*todo */ startScan = false
            )
        }
    }

    fun deleteImage() {
        updateReceiptImage(null)
        handleOcrResult(null)
        resetToIdle()
    }

    fun handleOcrResult(ocrResult: OcrResult?) {
        when {
            ocrResult == null -> {
                updateOcrState(OcrState.Processing, null)
            }

            ocrResult.errorMessage != null -> {
                updateOcrState(OcrState.Error(ocrResult.errorMessage), ocrResult)
            }

            else -> {
                updateOcrState(OcrState.Success, ocrResult)
            }
        }
    }


    private fun updateOcrState(newState: OcrState, result: OcrResult?) {
        stateManager.updateState { currentState ->
            currentState.copy(
                ocrState = newState,
                ocrResult = result,
                imagePath = result?.imagePathForDisplay ?: currentState.imagePath
            )
        }
    }

    fun isOcrResultReady(): Boolean {
        val currentState = stateManager.getCurrentState()
        return currentState.ocrResult?.groupedWordsIntoLines?.isNotBlank() == true
    }

    fun getOcrData(): Pair<String, List<GroupedTextLine>>? {
        val currentState = stateManager.getCurrentState()
        val ocrResult = currentState.ocrResult

        return if (ocrResult != null && ocrResult.groupedWordsIntoLines.isNotBlank()) {
            ocrResult.groupedWordsIntoLines to ocrResult.groupedLinesWithDetails
        } else {
            null
        }
    }

    fun resetToIdle() {
        updateOcrState(OcrState.Idle, null)
    }
}
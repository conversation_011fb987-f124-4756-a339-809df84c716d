package org.example.addReceipt.component.Image

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import org.example.core.domain.model.GroupedTextLine

@Composable
fun LineOverlay(
    groupedLines: List<GroupedTextLine>,
    imageWidth: Int,
    imageHeight: Int,
    modifier: Modifier = Modifier,
    lineColor: Color = Color.Red,
    lineWidth: Float = 3f
) {
    if (imageWidth <= 0 || imageHeight <= 0) {
        println(">>>> LINEOVERLAY: Image dimensions are invalid")
        return
    }

    Canvas(modifier = modifier.fillMaxSize()) {
        val scaleX = size.width / imageWidth
        val scaleY = size.height / imageHeight

        groupedLines.forEach { line ->
            drawLineForGroup(
                line = line,
                scaleX = scaleX,
                scaleY = scaleY,
                lineColor = lineColor,
                lineWidth = lineWidth
            )
        }
    }
}

private fun DrawScope.drawLineForGroup(
    line: GroupedTextLine,
    scaleX: Float,
    scaleY: Float,
    lineColor: Color,
    lineWidth: Float
) {
    val boundingBox = line.myBoundingBox

    // Calculate scaled coordinates
    val left = boundingBox.left * scaleX
    val right = boundingBox.right * scaleX
    val top = boundingBox.top * scaleY
    val bottom = boundingBox.bottom * scaleY

    // Calculate the middle Y position for the line
    val centerY = (top + bottom) / 2f

    // Draw horizontal line across the entire width of the grouped text
    drawLine(
        color = lineColor,
        start = Offset(left, centerY),
        end = Offset(right, centerY),
        strokeWidth = lineWidth
    )

    // Optionally, draw vertical indicators at the start and end
    drawLine(
        color = lineColor,
        start = Offset(left, top),
        end = Offset(left, bottom),
        strokeWidth = lineWidth / 2f
    )

    drawLine(
        color = lineColor,
        start = Offset(right, top),
        end = Offset(right, bottom),
        strokeWidth = lineWidth / 2f
    )
}

@Composable
fun CombinedTextOverlay(
    groupedLines: List<GroupedTextLine>,
    imageWidth: Int,
    imageHeight: Int,
    displayWidth: Float,
    displayHeight: Float,
    modifier: Modifier = Modifier,
    showLines: Boolean = true,
    showBoxes: Boolean = false,
    lineColor: Color = Color.Red,
    boxColor: Color = Color.Blue,
    lineWidth: Float = 3f,
    boxStrokeWidth: Float = 2f
) {
    if (imageWidth <= 0 || imageHeight <= 0 || displayWidth <= 0 || displayHeight <= 0) {
        return
    }

    val scaleX = displayWidth / imageWidth
    val scaleY = displayHeight / imageHeight

    Canvas(modifier = modifier.fillMaxSize()) {
        groupedLines.forEach { line ->
            if (showLines) {
                drawLineForGroup(
                    line = line,
                    scaleX = scaleX,
                    scaleY = scaleY,
                    lineColor = lineColor,
                    lineWidth = lineWidth
                )
            }

            if (showBoxes) {
                // Draw boxes around individual words
                line.elements.forEach { textBlock ->
                    val boundingBox = textBlock.myBoundingBox
                    val left = boundingBox.left * scaleX
                    val top = boundingBox.top * scaleY
                    val right = boundingBox.right * scaleX
                    val bottom = boundingBox.bottom * scaleY

                    drawRect(
                        color = boxColor,
                        topLeft = Offset(left, top),
                        size = androidx.compose.ui.geometry.Size(
                            width = right - left,
                            height = bottom - top
                        ),
                        style = Stroke(width = boxStrokeWidth)
                    )
                }
            }
        }
    }
}
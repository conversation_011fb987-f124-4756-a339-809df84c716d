package org.example.addReceipt

/**
 * Platform-agnostic interface for managing receipt image files
 */
interface ImageFileManager {
    /**
     * Moves temporary file to final location with receipt ID
     * @param tempFilePath Path to temporary file
     * @param receiptId Receipt ID for final filename
     * @return Final file path or null if failed
     */
    fun moveToFinalLocation(tempFilePath: String, receiptId: String): String?
    
    /**
     * Deletes a temporary file
     * @param tempFilePath Path to temporary file
     * @return true if deleted successfully
     */
    fun deleteTempFile(tempFilePath: String): Boolean
    
    /**
     * Deletes a receipt image file
     * @param receiptImagePath Path to receipt image file
     * @return true if deleted successfully
     */
    fun deleteReceiptFile(receiptImagePath: String): Bo<PERSON><PERSON>
    
    /**
     * Cleans up old temporary files
     */
    fun cleanupOldTempFiles()
}

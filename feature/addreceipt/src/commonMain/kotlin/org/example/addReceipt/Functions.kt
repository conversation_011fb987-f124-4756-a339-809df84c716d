package org.example.addReceipt

import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime

/**
 * Helper function to convert prices in cents to a displayable string format.
 * The value is converted from cents to the base currency unit (e.g., dollars, euros)
 * and then formatted with a specified separator for the decimal point.
 *
 * For example, if `valueInCents` is 12345 and `separator` is ",",
 * the function will return "123,45".
 * If `valueInCents` is 50 and `separator` is ".",
 *
 * @param priceInCents The monetary value in cents (Long).
 * @param separator The string to use as the decimal separator. Defaults to ",".
 * @return A string representing the value in base unit and subunit, formatted with the specified separator.
 */
fun formatPrice(priceInCents: Long, separator: String = ","): String {
    val baseUnit = priceInCents / 100
    val subunit = priceInCents % 100
    return "$baseUnit$separator${subunit.toString().padStart(2, '0')}"
}

/**
 * Converts a string representation of a currency amount to its value in cents (Long).
 *
 * This function handles various input formats, including those with commas or dots
 * as decimal separators, and correctly parses negative values. It aims to avoid
 * floating-point inaccuracies by performing manual string parsing.
 *
 * Examples:
 * - "123.45" -> 12345L
 * - "123,45" -> 12345L
 * - "123" -> 12300L
 * - "123." -> 12300L
 * - "123.4" -> 12340L
 * - "-123.45" -> -12345L
 * - "" -> 0L
 * - "invalid" -> 0L (or throws an exception, handled by returning 0L)
 *
 * @param amountText The currency amount as a string.
 * @return The amount in cents as a Long. Returns 0L for blank input or parsing errors.
 */
fun convertToStorageValue(amountText: String): Long {
    if (amountText.isBlank()) return 0L

    val cleanText = amountText.replace(",", ".")
    return try {
        // Parse the string manually to avoid floating-point errors
        val parts = cleanText.split(".")
        val wholePart = parts[0].toLongOrNull() ?: 0L
        val fractionalPart = if (parts.size > 1) {
            val fraction = parts[1].padEnd(2, '0').take(2)
            fraction.toLongOrNull() ?: 0L
        } else 0L

        val isNegative = cleanText.startsWith("-")
        val cents = wholePart * 100L + fractionalPart

        if (isNegative) -cents else cents
    } catch (e: Exception) {
        0L
    }
}

/**
 * Gets the current time as an ISO-8601 string by Clock.System.now().toString()
 */
fun getCurrentTimeString(): String {
//    println(Clock.System.now().toString())
    return Clock.System.now().toString()
}

/**
 * Converts a given date-time string into a human-readable relative time string
 * (e.g., "Dzisiaj", "Wczoraj", "Jutro", "X dni temu", "Za X dni").
 *
 * The function attempts to parse the `dateTimeString` in the following order:
 * 1. As an ISO 8601 string with a 'Z' (UTC timezone designator) using `Instant.parse()`.
 * 2. If the string contains 'T' but doesn't end with 'Z', it's parsed as a local date-time
 *    and converted to an Instant using the system's default timezone.
 * 3. As a "DD.MM.YYYY" formatted date string. If successful, it's treated as the start
 *    of that day (00:00:00) in the system's default timezone.
 *
 * If all parsing attempts fail, the original `dateTimeString` is returned.
 *
 * The relative time is calculated based on calendar days, not exact 24-hour periods.
 * For example, if `referenceTime` is "2023-01-02T10:00:00Z" and `dateTimeString`
 * resolves to "2023-01-01T23:00:00Z", it will be considered "Wczoraj".
 *
 * @param dateTimeString The date-time string to convert.
 *                       Supported formats:
 *                       - ISO 8601 with 'Z' (e.g., "2023-10-27T10:15:30Z")
 *                       - ISO 8601 without 'Z' (e.g., "2023-10-27T10:15:30" or "2023-10-27T10:15") - assumed local
 *                       - "DD.MM.YYYY" (e.g., "27.10.2023") - assumed as start of day in local timezone
 * @param referenceTime The reference `Instant` to compare against. Defaults to the current system time.
 */
fun toRelativeTime(
    dateTimeString: String,
    referenceTime: Instant = Clock.System.now()
): String {
    val targetInstant: Instant = try {
        // 1. Main approach: parse as Instant (ISO 8601 format with Z)
        Instant.parse(dateTimeString)
    } catch (e: Exception) {
        try {
            // 2. Try parsing as local datetime (without Z)
            if (dateTimeString.contains("T") && !dateTimeString.endsWith("Z")) {
                val localDateTime = LocalDateTime.parse(dateTimeString)
                localDateTime.toInstant(TimeZone.currentSystemDefault())
            } else {
                throw e // Re-throw to continue to next fallback
            }
        } catch (e2: Exception) {
            // 3. Fallback approach: parse as "DD.MM.YYYY"
            val parts = dateTimeString.split(".")
            if (parts.size == 3) {
                try {
                    val day = parts[0].toInt()
                    val month = parts[1].toInt()
                    val year = parts[2].toInt()

                    // Create LocalDateTime at the start of the day (00:00:00)
                    LocalDateTime(year, month, day, 0, 0, 0, 0)
                        .toInstant(TimeZone.currentSystemDefault())
                } catch (numEx: NumberFormatException) {
                    return dateTimeString
                } catch (argEx: IllegalArgumentException) {
                    return dateTimeString
                } catch (otherEx: Exception) {
                    return dateTimeString
                }
            } else {
                return dateTimeString
            }
        }
    }

    // Convert both instants to local date for calendar day comparison
    val targetLocalDate = targetInstant.toLocalDateTime(TimeZone.currentSystemDefault())
    val referenceLocalDate = referenceTime.toLocalDateTime(TimeZone.currentSystemDefault())

    // Calculate time difference in days
    val duration = referenceTime - targetInstant
    val daysDifference = duration.inWholeDays

    // Check if dates are on the same calendar day
    val isSameDay = targetLocalDate.year == referenceLocalDate.year &&
            targetLocalDate.monthNumber == referenceLocalDate.monthNumber &&
            targetLocalDate.dayOfMonth == referenceLocalDate.dayOfMonth

    // Check if target date is yesterday (one calendar day before)
    val isYesterday = targetLocalDate.year == referenceLocalDate.year &&
            targetLocalDate.monthNumber == referenceLocalDate.monthNumber &&
            targetLocalDate.dayOfMonth == referenceLocalDate.dayOfMonth - 1

    // Check if target date is tomorrow (one calendar day after)
    val isTomorrow = targetLocalDate.year == referenceLocalDate.year &&
            targetLocalDate.monthNumber == referenceLocalDate.monthNumber &&
            targetLocalDate.dayOfMonth == referenceLocalDate.dayOfMonth + 1

    // Construct relative response
    return when {
        // Future dates
        isTomorrow -> "Jutro"
        daysDifference < -1 -> {
            val daysInFuture = -daysDifference
            "Za $daysInFuture dni"
        }

        // Current day
        isSameDay -> "Dzisiaj"

        // Past dates
        isYesterday -> "Wczoraj"
        daysDifference > 1 -> "$daysDifference dni temu"

        // This else case should be unreachable with the new calendar-based checks
        else -> dateTimeString
    }
}

/**
 * Parses a date-time string, potentially in OpenAI's format (e.g., "2025-07-09T14:49"),
 * and converts it into a standard ISO 8601 format string that can be parsed by `Instant.parse()`.
 *
 * This function handles cases where the input string might be missing seconds or the 'Z' (UTC timezone designator).
 * - If the input string is empty, it returns the current time as an ISO 8601 string.
 * - If the input resembles OpenAI's format (contains 'T' but doesn't end with 'Z'):
 *     - If seconds are missing (e.g., "YYYY-MM-DDTHH:MM"), it appends ":00Z".
 *     - Otherwise (e.g., "YYYY-MM-DDTHH:MM:SS"), it appends "Z".
 * - It then attempts to parse the (potentially modified) string as an `Instant` and returns its string representation.
 * - If any parsing attempt fails, it falls back to returning the current time as an ISO 8601 string.
 *
 * @param openAiDateTime The date-time string to parse.
 * @return A properly formatted ISO 8601 string, or the current time string if parsing fails or input is empty.
 */
fun parseOpenAiDateTime(openAiDateTime: String): String {
    if (openAiDateTime.isEmpty()) {
        return getCurrentTimeString()
    }

    return try {
        // Handle OpenAI format: "2025-07-09T14:49"
        if (openAiDateTime.contains("T") && !openAiDateTime.endsWith("Z")) {
            // Add seconds but keep as local time
            if (openAiDateTime.count { it == ':' } == 1) {
                "${openAiDateTime}:00"
            } else {
                openAiDateTime
            }
        } else {
            openAiDateTime
        }
    } catch (e: Exception) {
        getCurrentTimeString()
    }
}
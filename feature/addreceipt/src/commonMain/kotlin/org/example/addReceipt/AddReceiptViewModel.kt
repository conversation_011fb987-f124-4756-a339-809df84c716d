package org.example.addReceipt

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import org.example.core.domain.model.Receipt
import org.example.core.domain.usecase.categories.GetTypesAndCategoriesUseCase


class AddReceiptViewModel(
    val state: MutableStateFlow<AddReceiptUiState>,
    private val stateManager: UiStateManager,
    private val receiptManager: ReceiptManager,
    private val openAiManager: OpenAiManager,
    private val exportManager: ExportManager,
    private val ocrManager: OcrManager,
    private val productManager: ProductManager,
    private val typesCategoriesUseCase: GetTypesAndCategoriesUseCase,
) : ViewModel() {

    val uiState: StateFlow<AddReceiptUiState> get() = state

    init {
        observeCategories()
        observeTypes()
    }


    private fun observeCategories() {
        viewModelScope.launch {
            typesCategoriesUseCase.getCategories().collect { categories ->
                stateManager.updateState { it.copy(categories = categories) }
            }
        }
    }

    private fun observeTypes() {
        viewModelScope.launch {
            typesCategoriesUseCase.getTypes().collect { types ->
                stateManager.updateState { it.copy(types = types) }
            }
        }
    }

    fun onEvent(event: AddReceiptEvent) {
        when (event) {
            // Load receipt for editing
            is AddReceiptEvent.LoadReceipt -> {
                loadReceipt(event.receiptId)
//                todo dodac komponent loading
                viewModelScope.launch {
                    receiptManager.loadReceipt(event.receiptId)
                }
            }

            // Image & OCR events
            is AddReceiptEvent.UpdateReceiptImage -> {
                ocrManager.updateReceiptImage(event.result)
            }

            is AddReceiptEvent.UpdateOcrResult -> {
                viewModelScope.launch {
                    ocrManager.handleOcrResult(event.ocrResult)
                    if (event.ocrResult != null) {
                        processOcrResultIfReady()
                    }
                }
            }

            is AddReceiptEvent.DeleteImage -> {
                ocrManager.deleteImage()
            }

            // Receipt basic info events
            is AddReceiptEvent.UpdateStoreName -> updateStoreName(event.storeName)
            is AddReceiptEvent.UpdateAddress -> updateStoreAddress(event.address)
            is AddReceiptEvent.UpdatePurchaseDate -> updatePurchaseDate(event.purchaseDate)
            is AddReceiptEvent.UpdateReceiptSum -> updateReceiptSum(event.receiptSum)
            is AddReceiptEvent.UpdatePurchaseMethod -> updatePurchaseMethod(event.purchaseMethod)
            AddReceiptEvent.ShowDateTimePicker -> stateManager.updateState {
                it.copy(
                    showDateTimePicker = true
                )
            }

            AddReceiptEvent.HideDateTimePicker -> stateManager.updateState {
                it.copy(
                    showDateTimePicker = false
                )
            }

            // Product events
            is AddReceiptEvent.UpdateProductName -> productManager.updateProductName(
                event.productId,
                event.name
            )

            is AddReceiptEvent.UpdateProductQty -> productManager.updateProductQuantity(
                event.productId,
                event.qty
            )

            is AddReceiptEvent.UpdateProductPrice -> productManager.updateProductPrice(
                event.productId,
                event.priceInCents
            )

            is AddReceiptEvent.UpdateProductTotal -> productManager.updateProductTotal(
                event.productId,
                event.totalInCents
            )

            is AddReceiptEvent.UpdateProductCategory -> productManager.updateProductCategory(
                event.productId,
                event.category
            )

            is AddReceiptEvent.UpdateProductType -> productManager.updateProductType(
                event.productId,
                event.type
            )

            AddReceiptEvent.AddNewProduct -> productManager.addNewProduct()
            is AddReceiptEvent.DeleteProduct -> productManager.deleteProduct(event.product.id)

            // Action events
            AddReceiptEvent.SaveReceipt -> saveReceipt()
            AddReceiptEvent.ShareReceiptCsv -> exportReceipt()
            AddReceiptEvent.ClearAllInputs -> stateManager.clearState()
            AddReceiptEvent.ClearError -> stateManager.clearError()

            // Retry events
            AddReceiptEvent.RetryOpenAi -> retryOpenAi()
            AddReceiptEvent.ClearSuccessStates -> clearSuccessStates()
            AddReceiptEvent.DuplicateReceipt -> duplicateReceipt()
            AddReceiptEvent.DeleteReceipt -> deleteReceipt()
            AddReceiptEvent.OmitStartScan -> {
                stateManager.updateState { it.copy(startScan = false, scanAlreadyStarted = true) }
            }

            AddReceiptEvent.StartScan -> {
                stateManager.updateState { it.copy(startScan = true) }
                println("AddReceiptViewModel: StartScan event handled, startScan: ${state.value.startScan}")
            }

            AddReceiptEvent.ResetScanFlag -> {
                stateManager.updateState { it.copy(scanAlreadyStarted = false) }
            }

            AddReceiptEvent.ScanRecovered -> {
//                stateManager.updateState { it.copy(scanRecovered = true) }
            }
        }
    }

    private fun updateStoreName(storeName: String) {
        stateManager.updateState { it.copy(storeName = storeName) }
    }

    private fun updateStoreAddress(address: String) {
        stateManager.updateState { it.copy(storeAddress = address) }
    }

    private fun updatePurchaseDate(purchaseDate: String) {
        stateManager.updateState { it.copy(purchaseDate = purchaseDate) }
    }

    private fun updateReceiptSum(receiptSumInCents: Long) {
        stateManager.updateState { it.copy(receiptSumInCents = receiptSumInCents) }
    }

    private fun updatePurchaseMethod(purchaseMethod: String) {
        stateManager.updateState { it.copy(purchaseMethod = purchaseMethod) }
    }

    private fun loadReceipt(receiptId: String) {
        viewModelScope.launch {
            stateManager.clearError() // todo tego tu chyba nie powinno byc?
            stateManager.updateState { it.copy(isLoadingReceipt = true) }
            val result = receiptManager.loadReceipt(receiptId)
            if (result.isSuccess) {
                populateStateFromReceipt(result.getOrNull()!!)
                stateManager.updateState {
                    it.copy(
                        isLoadingReceipt = false,
                        isEditMode = true,
                        editingReceiptId = receiptId
                    )
                }
            } else if (result.isFailure) {
                stateManager.updateState { it.copy(isLoadingReceipt = false) }
                val exception = result.exceptionOrNull()
                stateManager.setError(UiError.ErrorMessage("Wystąpił błąd podczas ładowania: ${exception?.message}"))
            }
        }
    }

    private fun populateStateFromReceipt(receipt: Receipt) {
        val productDisplayables = receipt.products.map { product ->
            ProductDisplayable(
                id = product.id,
                name = product.name,
                qty = product.qty,
                priceInCents = product.priceInCents,
                totalInCents = product.totalInCents,
                category = product.category,
                type = product.type,
                purchaseDate = product.purchaseDate,
                totalNotBlank = product.totalInCents > 0L,
                ocrGroupedTextLine = product.ocrGroupedTextLine
            )
        }

        stateManager.updateState { currentState ->
            currentState.copy(
                storeName = receipt.name,
                receiptSumInCents = receipt.receiptSum ?: 0L,
                purchaseDate = receipt.purchaseDate,
                purchaseMethod = receipt.purchaseMethod,
                imagePath = receipt.imagePath,
                products = productDisplayables.ifEmpty { listOf(ProductDisplayable()) }
            )
        }
    }

    private fun deleteReceipt() {
        viewModelScope.launch {
            val receiptId = stateManager.getCurrentState().editingReceiptId
            if (receiptId != null) {
                stateManager.updateState { it.copy(deleteState = DeleteState.Deleting) }
                val deleteResult = receiptManager.deleteReceipt(receiptId)
                if (deleteResult.isSuccess) {
                    stateManager.updateState { it.copy(deleteState = DeleteState.Success) }
                } else {
                    val exception = deleteResult.exceptionOrNull()
                    val errorMessage = "Wystąpił błąd podczas usuwania: ${exception?.message}"
                    stateManager.updateState { it.copy(deleteState = DeleteState.Error(errorMessage)) }
                    stateManager.setError(UiError.ErrorMessage(errorMessage)) //TODO trzymac to ui error? czy miec rozdzielenie pomiedzy errorami?
                }
            }
        }
    }

    /** Duplicate receipt (without image, with current date and time), and load it
     * */
    private fun duplicateReceipt() {
        viewModelScope.launch {
            stateManager.updateState { it.copy(duplicateState = DuplicateState.Duplicating) }
            val currentState = stateManager.getCurrentState()
            val duplicateResult = receiptManager.duplicateReceipt(currentState)
            if (duplicateResult.isSuccess) {
                val receiptId = duplicateResult.getOrNull()?.id
                stateManager.updateState {
                    if (receiptId != null) {
                        it.copy(duplicateState = DuplicateState.Success)
                    } else {
                        it.copy(duplicateState = DuplicateState.Error("Błąd podczas duplikowania: receiptId is null"))
                    }
                }
                stateManager.updateState {
                    it.copy(
                        isLoadingReceipt = true
                    )
                }
                val loadReceipt = receiptManager.loadReceipt(receiptId!!)
                if (loadReceipt.isSuccess) {
                    stateManager.updateState { it.copy(isLoadingReceipt = false) }
                } else {
                    stateManager.setError(UiError.ErrorMessage("Wystąpił błąd podczas ładowania duplikatu: ${loadReceipt.exceptionOrNull()?.message}"))
                }
            } else {
                val exception = duplicateResult.exceptionOrNull()
                val errorMessage = "Wystąpił błąd podczas duplikowania: ${exception?.message}"
                stateManager.updateState {
                    it.copy(
                        duplicateState = DuplicateState.Error(
                            errorMessage
                        )
                    )
                }
                stateManager.setError(UiError.ErrorMessage(errorMessage))
            }
        }
    }

    private fun processOcrResultIfReady() {
        if (ocrManager.isOcrResultReady()) {
            val ocrData = ocrManager.getOcrData()
            if (ocrData != null) {
                val (ocrText, originalOcrLines) = ocrData
                viewModelScope.launch {
                    openAiManager.parseReceiptText(ocrText, originalOcrLines)
                }
            }
        } else {
            openAiManager.updateOpenAiState(
                OpenAiState.Error(
                    message = "OCR returned empty text.",
                    errorType = OpenAiErrorType.PARSING_ERROR
                )
            )
        }
    }

    private fun saveReceipt() {
        val validationResult = productManager.validateProducts()
        if (!validationResult.isValid) {
            stateManager.setError(UiError.ErrorMessage(validationResult.errorMessage))
            return
        }
        viewModelScope.launch {
            stateManager.updateState { it.copy(saveState = SaveState.Saving) }
            val currentState = stateManager.getCurrentState()
//            when (val saveResult = receiptManager.saveReceipt(currentState)){
//             is Result.Success  -> {} // TODO? nie mam wlasnej klasy Result a kotlinowa zwraca Result<Unit>
//            }
            val saveResult = receiptManager.saveReceipt(currentState)
            if (saveResult.isSuccess) {

                stateManager.updateState { it.copy(saveState = SaveState.Success) }
            } else if (saveResult.isFailure) {

                val exception = saveResult.exceptionOrNull()
                val errorMessage = if (currentState.isEditMode) {
                    "Wystąpił błąd podczas aktualizacji: ${exception?.message}"
                } else {
                    "Wystąpił błąd podczas zapisywania: ${exception?.message}"
                }
                stateManager.updateState { it.copy(saveState = SaveState.Error(errorMessage)) }
            } else {
                stateManager.updateState { it.copy(saveState = SaveState.Error("Błąd saveResult not success and not failure: ${saveResult.exceptionOrNull()?.message}")) }
            }

        }
    }

    private fun exportReceipt() {
        val validationResult = productManager.validateProducts()
        if (!validationResult.isValid) {
            stateManager.setError(UiError.ErrorMessage(validationResult.errorMessage))
            return
        }

        viewModelScope.launch {
            exportManager.exportReceipt()
        }
    }

    private fun retryOpenAi() {
        if (ocrManager.isOcrResultReady()) {
            val ocrData = ocrManager.getOcrData()
            if (ocrData != null) {
                val (ocrText, originalOcrLines) = ocrData
                viewModelScope.launch {
                    openAiManager.parseReceiptText(ocrText, originalOcrLines)
                }
            }
        }
    }

    private fun clearSuccessStates() {
        val currentState = stateManager.getCurrentState()

        if (currentState.ocrState is OcrState.Success) {
            ocrManager.resetToIdle()
        }
        if (currentState.openAiState is OpenAiState.Success) {
            openAiManager.resetToIdle()
        }
        if (currentState.exportState is ExportState.Success) {
            exportManager.resetToIdle()
        }
        if (currentState.saveState is SaveState.Success) {
            stateManager.updateState { it.copy(saveState = SaveState.Idle) }
        }
        if (currentState.duplicateState is DuplicateState.Success) {
            stateManager.updateState { it.copy(duplicateState = DuplicateState.Idle) }
        }
    }
}

// ============================================================================
// MARK: - Data Classes
// ============================================================================

data class ValidationResult(
    val isValid: Boolean,
    val errorMessage: String = ""
)
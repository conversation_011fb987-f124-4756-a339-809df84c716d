package org.example.addReceipt.scanner

import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.IntSize
import org.example.core.domain.model.GroupedTextLine
import org.example.core.domain.model.MyTextBlock

data class OcrResult(
    val groupedWordsIntoLines: String,
    val myTextBlocks: List<MyTextBlock> = emptyList(),
    val groupedLinesWithDetails: List<GroupedTextLine> = emptyList(),
    val imagePathForDisplay: String? = null,
    val errorMessage: String? = null
) {
    constructor(errorMessage: String) : this(
        groupedWordsIntoLines = "",
        imagePathForDisplay = null,
        errorMessage = errorMessage
    )
}

// Platform-agnostic interface for text recognition functionality
interface TextRecognizerML {
    /**
     * Recognizes text in an image
     * @param imagePath The path to the image file
     * @return The recognized text, or an error message
     */
    suspend fun recognizeText(imagePath: String): String

    /**
     * Recognizes text in an image with detailed positioning information
     * @param imagePath The path to the image file
     * @return OcrResult containing text and positioning data
     */
    suspend fun recognizeTextWithDetails(imagePath: String): OcrResult
}

/**
 * Creates and remembers a platform-specific implementation of TextRecognizerML
 */
@Composable
expect fun rememberTextRecognizer(): TextRecognizerML

@Composable
expect fun getBitmapSize(imagePath: String): IntSize
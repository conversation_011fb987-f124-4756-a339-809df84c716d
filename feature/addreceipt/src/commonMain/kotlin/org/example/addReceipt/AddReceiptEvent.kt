package org.example.addReceipt

import org.example.addReceipt.scanner.OcrResult

sealed class AddReceiptEvent {
    object OmitStartScan : AddReceiptEvent()
    object StartScan : AddReceiptEvent()
    object ScanRecovered : AddReceiptEvent()
    data class LoadReceipt(val receiptId: String) : AddReceiptEvent()
    data class UpdateReceiptImage(val result: String?) : AddReceiptEvent()
    data class UpdateStoreName(val storeName: String) : AddReceiptEvent()
    data class UpdateAddress(val address: String) : AddReceiptEvent()
    data class UpdatePurchaseDate(val purchaseDate: String) : AddReceiptEvent()
    data class UpdateReceiptSum(val receiptSum: Long) : AddReceiptEvent()
    data class UpdatePurchaseMethod(val purchaseMethod: String) : AddReceiptEvent()
    object ShowDateTimePicker : AddReceiptEvent()
    object HideDateTimePicker : AddReceiptEvent()

    data class UpdateProductName(val productId: String, val name: String) : AddReceiptEvent()
    data class UpdateProductQty(val productId: String, val qty: String) : AddReceiptEvent()
    data class UpdateProductPrice(val productId: String, val priceInCents: Long) : AddReceiptEvent()
    data class UpdateProductTotal(val productId: String, val totalInCents: Long) : AddReceiptEvent()
    data class UpdateProductCategory(val productId: String, val category: String) :
        AddReceiptEvent()

    data class UpdateProductType(val productId: String, val type: String) : AddReceiptEvent()
    object AddNewProduct : AddReceiptEvent()
    data class DeleteProduct(val product: ProductDisplayable) : AddReceiptEvent()

    object SaveReceipt : AddReceiptEvent()
    data object ShareReceiptCsv : AddReceiptEvent()
    object ClearAllInputs : AddReceiptEvent()
    object DeleteImage : AddReceiptEvent()
    object DuplicateReceipt : AddReceiptEvent()
    object DeleteReceipt : AddReceiptEvent()

    object ClearError : AddReceiptEvent()

    // Retry events
    object RetryOpenAi : AddReceiptEvent()
    object ClearSuccessStates : AddReceiptEvent()

    data class UpdateOcrResult(val ocrResult: OcrResult?) : AddReceiptEvent()
}
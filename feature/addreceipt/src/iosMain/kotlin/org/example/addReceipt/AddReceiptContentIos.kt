package org.example.addReceipt

import androidx.compose.runtime.Composable
import org.example.addReceipt.scanner.TextRecognizerML

/**
 * iOS implementation to check for recovered scan results
 * iOS doesn't need this as it handles app lifecycle differently
 */
@Composable
actual fun CheckForRecoveredScan(
    onEvent: (AddReceiptEvent) -> Unit,
    textRecognizer: TextRecognizerML
) {
    // iOS doesn't need process death recovery for document scanning
    // as the system handles app lifecycle differently
    // This is a no-op Composable
}

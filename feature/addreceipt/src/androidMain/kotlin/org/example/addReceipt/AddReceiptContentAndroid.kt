package org.example.addReceipt

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.example.addReceipt.scanner.TextRecognizerML
import java.io.File

/**
 * Android implementation to check for recovered scan results after process death
 */
@Composable
actual fun CheckForRecoveredScan(
    onEvent: (AddReceiptEvent) -> Unit,
    textRecognizer: TextRecognizerML
) {
    val context = LocalContext.current

    androidx.compose.runtime.LaunchedEffect(Unit) {
        println("AddReceiptContentAndroid: Checking for recovered scan")

        val prefs = context.getSharedPreferences("scan_recovery", Context.MODE_PRIVATE)
        val recoveredPath = prefs.getString("recovered_scan_path", null)
        val recoveryTimestamp = prefs.getLong("recovery_timestamp", 0)

        println("AddReceiptContentAndroid: Recovered path: $recoveredPath, timestamp: $recoveryTimestamp")

        if (recoveredPath != null && recoveryTimestamp > 0) {
            val file = File(recoveredPath)
            if (file.exists() && file.length() > 0) {
                println("AddReceiptContentAndroid: Found recovered scan file: $recoveredPath")

                // Clear the recovery data immediately
                prefs.edit()
                    .remove("recovered_scan_path")
                    .remove("recovery_timestamp")
                    .apply()

                // Process the recovered scan
                processRecoveredScan(recoveredPath, onEvent, textRecognizer)
            } else {
                println("AddReceiptContentAndroid: Recovered file does not exist: $recoveredPath")
                // Clear invalid recovery data
                prefs.edit()
                    .remove("recovered_scan_path")
                    .remove("recovery_timestamp")
                    .apply()
            }
        } else {
            println("AddReceiptContentAndroid: No recovered scan found")
        }
    }
}

private fun processRecoveredScan(
    filePath: String,
    onEvent: (AddReceiptEvent) -> Unit,
    textRecognizer: TextRecognizerML
) {
    println("AddReceiptContentAndroid: Processing recovered scan: $filePath")

    // Update the image first
    onEvent(AddReceiptEvent.UpdateReceiptImage(filePath))

    // Start OCR processing in background
    CoroutineScope(Dispatchers.IO).launch {
        try {
            onEvent(AddReceiptEvent.ScanCompleted) // Mark scan as completed to prevent re-triggering
            println("AddReceiptContentAndroid: Starting OCR for recovered scan")
            onEvent(AddReceiptEvent.UpdateOcrResult(null)) // Set to loading state
            val ocrResult = textRecognizer.recognizeTextWithDetails(filePath)
            onEvent(AddReceiptEvent.UpdateOcrResult(ocrResult))
            println("AddReceiptContentAndroid: OCR completed for recovered scan")
        } catch (e: Exception) {
            println("AddReceiptContentAndroid: Error during OCR: ${e.message}")
            onEvent(AddReceiptEvent.UpdateOcrResult(null))
        }
    }
}

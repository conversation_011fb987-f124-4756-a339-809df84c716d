package org.example.addReceipt.ocr

import com.google.mlkit.vision.text.Text
import org.example.core.domain.model.GroupedTextLine
import org.example.core.domain.model.MyBoundingBox
import org.example.core.domain.model.MyTextBlock
import kotlin.math.abs

class TextGrouper {
    companion object {
        private const val TAG = "TextGrouper" // Użyjemy tego TAGu do logowania
        private const val DEFAULT_TOLERANCE_MULTIPLIER = 0.6f

        fun groupWordsIntoLines(
            visionText: Text,
            toleranceMultiplier: Float = DEFAULT_TOLERANCE_MULTIPLIER
        ): String {
            val groupedLinesDetails = groupWordsIntoLinesWithDetails(visionText, toleranceMultiplier)
            val resultText = groupedLinesDetails.joinToString("\n") { it.text }

            // Logowanie całego pogrupowanego tekstu
//            Log.d(TAG, "--- Final Grouped Text START ---")
//            Log.d(TAG, resultText)
//            Log.d(TAG, "--- Final Grouped Text END ---")

            return resultText
        }

        fun groupWordsIntoLinesWithDetails(
            visionText: Text,
            toleranceMultiplier: Float = DEFAULT_TOLERANCE_MULTIPLIER
        ): List<GroupedTextLine> {
            val elements = extractElementsWithBounds(visionText)
            if (elements.isEmpty()) {
                return emptyList()
            }

            // Logowanie elementów z ich confidence
//            Log.d(TAG, "--- Elements with Confidence START ---")
//            elements.forEachIndexed { index, el ->
//                Log.d(TAG, "Element[$index]: '${el.text}' (Confidence: ${el.confidence})")
//            }
//            Log.d(TAG, "--- Elements with Confidence END ---")


            val heights = elements.map { it.height }
            val averageElementHeight = heights.average().takeIf { !it.isNaN() } ?: 0.0
            val dynamicTolerance = averageElementHeight * toleranceMultiplier

            elements.sortBy { it.x }
            elements.sortBy { it.y }

            val resultGroupedLines = mutableListOf<GroupedTextLine>()
            var currentLineFormingElements = mutableListOf<ElementWithBounds>()

            for (element in elements) {
                val elementCenterY = element.y + element.height / 2.0

                if (currentLineFormingElements.isEmpty()) {
                    currentLineFormingElements.add(element)
                } else {
                    val currentLineAvgCenterY = currentLineFormingElements.map { it.y + it.height / 2.0 }.average()
                    val diffToCurrentLineAvgCenter = abs(elementCenterY - currentLineAvgCenterY)

                    if (diffToCurrentLineAvgCenter <= dynamicTolerance) {
                        currentLineFormingElements.add(element)
                    } else {
                        if (currentLineFormingElements.isNotEmpty()) {
                            currentLineFormingElements.sortBy { it.x }
                            val lineToAdd = buildGroupedTextLineFromElements(currentLineFormingElements)
                            resultGroupedLines.add(lineToAdd)
                        }
                        currentLineFormingElements = mutableListOf(element)
                    }
                }
            }

            if (currentLineFormingElements.isNotEmpty()) {
                currentLineFormingElements.sortBy { it.x }
                val lineToAdd = buildGroupedTextLineFromElements(currentLineFormingElements)
                resultGroupedLines.add(lineToAdd)
            }

            // Logowanie sfinalizowanych linii i pewności ich elementów składowych
//            Log.d(TAG, "--- Grouped Lines with Element Confidences START ---")
//            resultGroupedLines.forEachIndexed { lineIndex, groupedLine ->
//                Log.d(TAG, "Line ${lineIndex + 1}: \"${groupedLine.text}\"")
//                groupedLine.elements.forEachIndexed { elementIndex, myTextBlock ->
//                    Log.d(TAG, "  Element $elementIndex: \"${myTextBlock.text}\" (Confidence: ${myTextBlock.confidence})")
//                }
//            }
//            Log.d(TAG, "--- Grouped Lines with Element Confidences END ---")


            return resultGroupedLines
        }

        private fun extractElementsWithBounds(visionText: Text): MutableList<ElementWithBounds> {
            val elementsWithBounds = mutableListOf<ElementWithBounds>()
            for (block in visionText.textBlocks) {
                // Można dodać logowanie confidence dla bloku, jeśli jest dostępne i potrzebne
                // Log.d(TAG, "TextBlock: '${block.text.replace("\n", " ")}' (Confidence: ${block.confidence})");
                for (line in block.lines) {
                    // Logowanie confidence dla linii (bezpośrednio z ML Kit)
                    // Log.d(TAG, "  MLKit Line: '${line.text}' (Confidence: ${line.confidence})");
                    for (element in line.elements) {
                        element.boundingBox?.let { bbox ->
                            elementsWithBounds.add(
                                ElementWithBounds(
                                    text = element.text,
                                    x = bbox.left,
                                    y = bbox.top,
                                    width = bbox.width(),
                                    height = bbox.height(),
                                    confidence = element.confidence ?: 0.0f // Użyj 0.0f jeśli null, dla jasności
                                )
                            )
                        }
                    }
                }
            }
            return elementsWithBounds
        }

        private fun buildGroupedTextLineFromElements(lineElements: List<ElementWithBounds>): GroupedTextLine {
            if (lineElements.isEmpty()) {
                return GroupedTextLine("", MyBoundingBox(0f, 0f, 0f, 0f), emptyList())
            }
            val sortedLineElements = lineElements.sortedBy { it.x }

            val leftmost = sortedLineElements.minOf { it.x }
            val topmost = sortedLineElements.minOf { it.y }
            val rightmost = sortedLineElements.maxOf { it.x + it.width }
            val bottommost = sortedLineElements.maxOf { it.y + it.height }

            val lineMyBoundingBox = MyBoundingBox(
                left = leftmost.toFloat(),
                top = topmost.toFloat(),
                right = rightmost.toFloat(),
                bottom = bottommost.toFloat()
            )

            val myTextBlocksInLine = sortedLineElements.map { el ->
                MyTextBlock(
                    text = el.text,
                    myBoundingBox = MyBoundingBox(
                        left = el.x.toFloat(),
                        top = el.y.toFloat(),
                        right = (el.x + el.width).toFloat(),
                        bottom = (el.y + el.height).toFloat()
                    ),
                    confidence = el.confidence // Przekazanie confidence z ElementWithBounds
                )
            }

            return GroupedTextLine(
                text = sortedLineElements.joinToString(" ") { it.text },
                myBoundingBox = lineMyBoundingBox,
                elements = myTextBlocksInLine
            )
        }
    }

    internal data class ElementWithBounds(
        val text: String,
        val x: Int,
        val y: Int,
        val width: Int,
        val height: Int,
        val confidence: Float // confidence dla pojedynczego elementu (słowa/tokenu)
    )
}
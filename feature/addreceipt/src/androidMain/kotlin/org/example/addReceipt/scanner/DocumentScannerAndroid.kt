package org.example.addReceipt.scanner

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.IntentSender
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import com.google.mlkit.vision.documentscanner.GmsDocumentScannerOptions
import com.google.mlkit.vision.documentscanner.GmsDocumentScanning
import com.google.mlkit.vision.documentscanner.GmsDocumentScanningResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.File
import kotlin.coroutines.resume
import java.lang.ref.WeakReference

@Composable
actual fun rememberDocumentScanner(): DocumentScanner {
    val context = LocalContext.current
    return remember { AndroidDocumentScanner(context) }
}

class AndroidDocumentScanner(private val context: Context) : DocumentScanner {
    private val imageManager = ReceiptImageManager(context)
    private val state = DocumentScannerState.getInstance()
    override suspend fun scanDocument(): String? = suspendCancellableCoroutine { continuation ->
//        println("AndroidDocumentScanner: Starting document scan")

        val options = GmsDocumentScannerOptions.Builder()
            .setScannerMode(GmsDocumentScannerOptions.SCANNER_MODE_BASE)
            .setGalleryImportAllowed(true)
            .setPageLimit(1)
            .setResultFormats(GmsDocumentScannerOptions.RESULT_FORMAT_JPEG)
            .build()

        val scanner = GmsDocumentScanning.getClient(options)

        try {
            // Store the continuation for later use
            state.setActiveScanner(this)
            state.setActiveContinuation(continuation)

//            println("AndroidDocumentScanner: Getting start scan intent")
            scanner.getStartScanIntent(context as Activity)
                .addOnSuccessListener { intentSender ->
                    try {
//                        println("AndroidDocumentScanner: Starting intent sender via launcher")
                        val request = IntentSenderRequest.Builder(intentSender).build()
                        state.getLauncher()?.launch(request)
                    } catch (e: Exception) {
                        println("AndroidDocumentScanner: Error starting intent sender: ${e.message}")
                        cleanupAndResume(null)
                    }
                }
                .addOnFailureListener { e ->
                    println("AndroidDocumentScanner: Failed to get start scan intent: ${e.message}")
                    cleanupAndResume(null)
                }

            // Set up cancellation
            continuation.invokeOnCancellation {
                println("AndroidDocumentScanner: Scan cancelled")
                cleanupAndResume(null)
            }
        } catch (e: Exception) {
            println("AndroidDocumentScanner: Exception during scan: ${e.message}")
            cleanupAndResume(null)
        }
    }

    private fun cleanupAndResume(result: String?) {
//        println("AndroidDocumentScanner: Cleaning up with result: $result")
        val continuation = state.getActiveContinuation()
        if (continuation == null) {
            println("AndroidDocumentScanner: No active continuation to resume")
            return
        }
        continuation.resume(result)
        state.clearActiveContinuation()
        println("AndroidDocumentScanner: Cleanup completed")
    }

    companion object {
        private val state = DocumentScannerState.getInstance()

        // Method to set the launcher from MainActivity
        fun setLauncher(activityLauncher: ActivityResultLauncher<IntentSenderRequest>) {
            state.setLauncher(activityLauncher)
        }

        // Method to set application context for process death recovery
        fun setAppContext(context: Context) {
            state.setAppContext(context)
        }

        // This method should be called from the Activity Result callback
        fun handleActivityResult(result: ActivityResult) {
//            println("AndroidDocumentScanner: ===== ACTIVITY RESULT RECEIVED =====")
//            println("AndroidDocumentScanner: Result code: ${result.resultCode}")
            state.logCurrentState()

            val scanner = state.getActiveScanner()
            if (scanner == null) {
                println("AndroidDocumentScanner: No active scanner (weak reference cleared)")
                println("AndroidDocumentScanner: This indicates process death - attempting recovery")
                handleProcessDeathRecovery(result)
                return
            }

//            println("AndroidDocumentScanner: Active scanner found, processing result normally")
            processActivityResult(result, scanner)
        }

        private fun handleProcessDeathRecovery(result: ActivityResult) {
            println("AndroidDocumentScanner: ===== PROCESS DEATH RECOVERY =====")

            if (result.resultCode != Activity.RESULT_OK || result.data == null) {
                println("AndroidDocumentScanner: No valid result to recover")
                return
            }

            try {
                val scanningResult = GmsDocumentScanningResult.fromActivityResultIntent(result.data)
                val pages = scanningResult?.pages

                if (pages != null && pages.isNotEmpty()) {
                    val firstPage = pages[0]
                    val imageUri = firstPage.imageUri
                    println("AndroidDocumentScanner: Recovering scan result: $imageUri")

                    // Move file I/O to background thread
                    CoroutineScope(Dispatchers.IO).launch {
                        saveRecoveredScanToPrefs(imageUri.toString())
                    }

                } else {
                    println("AndroidDocumentScanner: No pages in recovered result")
                }
            } catch (e: Exception) {
                println("AndroidDocumentScanner: Error during recovery: ${e.message}")
                e.printStackTrace()
            }
        }

        private fun saveRecoveredScanToPrefs(uriString: String) {
            try {
                val context = state.getAppContext()
                if (context == null) {
                    println("AndroidDocumentScanner: No app context available for recovery")
                    return
                }

                println("AndroidDocumentScanner: Saving recovered scan URI to SharedPreferences: $uriString")

                // Copy the file immediately using ReceiptImageManager
                val imageManager = ReceiptImageManager(context)
                val tempFile = imageManager.createTempScanFile()

                val uri = android.net.Uri.parse(uriString)
                context.contentResolver.openInputStream(uri)?.use { input ->
                    tempFile.outputStream().use { output ->
                        input.copyTo(output)
                    }
                }

                if (tempFile.exists() && tempFile.length() > 0) {
                    println("AndroidDocumentScanner: Successfully copied recovered file: ${tempFile.absolutePath}")

                    // Save to SharedPreferences for UI to pick up
                    val prefs = context.getSharedPreferences("scan_recovery", Context.MODE_PRIVATE)
                    prefs.edit()
                        .putString("recovered_scan_path", tempFile.absolutePath)
                        .putLong("recovery_timestamp", System.currentTimeMillis())
                        .apply()

                    println("AndroidDocumentScanner: Saved recovery info to SharedPreferences")
                } else {
                    println("AndroidDocumentScanner: Failed to copy recovered file")
                }

            } catch (e: Exception) {
                println("AndroidDocumentScanner: Error during recovery save: ${e.message}")
                e.printStackTrace()
            }
        }

        private fun processActivityResult(result: ActivityResult, scanner: AndroidDocumentScanner) {
            if (result.resultCode == Activity.RESULT_OK && result.data != null) {
                println("AndroidDocumentScanner: Result OK with data")
                val scanningResult = GmsDocumentScanningResult.fromActivityResultIntent(result.data)
                val pages = scanningResult?.pages
                if (pages != null && pages.isNotEmpty()) {
                    println("AndroidDocumentScanner: Got ${pages.size} pages")
                    val firstPage = pages[0]
                    val imageUri = firstPage.imageUri
                    println("AndroidDocumentScanner: Image URI: $imageUri")
                    val context = scanner.context

                    try {
                        // Create temporary file and copy image
                        val tempFile = scanner.imageManager.createTempScanFile()
                        println("AndroidDocumentScanner: Copying image from URI to temp file")
                        println("AndroidDocumentScanner: URI: $imageUri")
                        println("AndroidDocumentScanner: Temp file: ${tempFile.absolutePath}")

                        context.contentResolver.openInputStream(imageUri)?.use { input ->
                            tempFile.outputStream().use { output ->
                                input.copyTo(output)
                            }
                        }

                        // Verify the file exists and has content
                        if (tempFile.exists() && tempFile.length() > 0) {
                            println("AndroidDocumentScanner: Successfully saved temp image, size: ${tempFile.length()} bytes")
                            scanner.cleanupAndResume(tempFile.absolutePath)
                        } else {
                            println("AndroidDocumentScanner: Temp file was not created or is empty")
                            scanner.cleanupAndResume(null)
                        }
                    } catch (e: Exception) {
                        println("AndroidDocumentScanner: Error saving image: ${e.message}")
                        e.printStackTrace()
                        scanner.cleanupAndResume(null)
                    }
                } else {
                    println("AndroidDocumentScanner: No pages in result")
                    scanner.cleanupAndResume(null)
                }
            } else {
                println("AndroidDocumentScanner: Result not OK or no data")
                scanner.cleanupAndResume(null)
            }
        }
    }
}


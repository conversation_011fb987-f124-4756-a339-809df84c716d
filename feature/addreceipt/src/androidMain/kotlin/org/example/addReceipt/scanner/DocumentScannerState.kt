package org.example.addReceipt.scanner

import android.content.Context
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import kotlinx.coroutines.CancellableContinuation
import java.lang.ref.WeakReference

/**
 * Singleton to manage document scanner state safely
 * Replaces static variables to avoid memory leaks and improve testability
 */
class DocumentScannerState private constructor() {
    
    private var activeScanner: WeakReference<AndroidDocumentScanner>? = null
    private var activeContinuation: CancellableContinuation<String?>? = null
    private var launcher: ActivityResultLauncher<IntentSenderRequest>? = null
    private var appContext: WeakReference<Context>? = null
    
    companion object {
        @Volatile
        private var INSTANCE: DocumentScannerState? = null
        
        fun getInstance(): DocumentScannerState {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DocumentScannerState().also { INSTANCE = it }
            }
        }
    }
    
    fun setActiveScanner(scanner: AndroidDocumentScanner) {
        activeScanner = WeakReference(scanner)
        println("DocumentScannerState: Active scanner set")
    }
    
    fun getActiveScanner(): AndroidDocumentScanner? {
        return activeScanner?.get()
    }
    
    fun setActiveContinuation(continuation: CancellableContinuation<String?>) {
        activeContinuation = continuation
        println("DocumentScannerState: Active continuation set")
    }
    
    fun getActiveContinuation(): CancellableContinuation<String?>? {
        return activeContinuation
    }
    
    fun clearActiveContinuation() {
        activeContinuation = null
        println("DocumentScannerState: Active continuation cleared")
    }
    
    fun setLauncher(activityLauncher: ActivityResultLauncher<IntentSenderRequest>) {
        launcher = activityLauncher
        println("DocumentScannerState: Launcher set")
    }
    
    fun getLauncher(): ActivityResultLauncher<IntentSenderRequest>? {
        return launcher
    }
    
    fun setAppContext(context: Context) {
        appContext = WeakReference(context.applicationContext)
        println("DocumentScannerState: App context set")
    }
    
    fun getAppContext(): Context? {
        return appContext?.get()
    }
    
    fun clearAll() {
        activeScanner = null
        activeContinuation = null
        launcher = null
        appContext = null
        println("DocumentScannerState: All state cleared")
    }
    
    fun logCurrentState() {
        println("DocumentScannerState: Current state:")
        println("  - Active scanner: ${activeScanner?.get()}")
        println("  - Active continuation: $activeContinuation")
        println("  - Launcher: $launcher")
        println("  - App context: ${appContext?.get()}")
    }
}

package org.example.home.domain

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.AppSettingsAlt
import androidx.compose.material.icons.rounded.Category
import androidx.compose.material.icons.rounded.Settings
import androidx.compose.material.icons.rounded.ShoppingBag
import androidx.compose.ui.graphics.vector.ImageVector

enum class DrawerItem(
    val title: String,
    val icon: ImageVector
){
    Categories(
        title = "Kategorie",
        icon = Icons.Rounded.Category
    ),
    Budget(
        title = "Budżet tygodniowy",
        icon= Icons.Rounded.ShoppingBag
    ),
    ConfigureCsv(
        title = "Konfiguruj CSV",
        icon= Icons.Rounded.AppSettingsAlt
    ),
    Settings(
        title = "Us<PERSON>wi<PERSON>",
        icon = Icons.Rounded.Settings
    )
}
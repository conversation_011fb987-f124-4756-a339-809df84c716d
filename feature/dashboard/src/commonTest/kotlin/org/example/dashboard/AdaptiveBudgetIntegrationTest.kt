package org.example.dashboard

import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import kotlinx.datetime.LocalDate
import org.example.core.domain.model.Receipt
import org.example.core.domain.usecase.categories.GetTypesAndCategoriesUseCase
import org.example.core.domain.usecase.receipt.GetReceiptsUseCase
import org.example.core.utils.DateUtils
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@OptIn(ExperimentalCoroutinesApi::class)
class AdaptiveBudgetIntegrationTest {

    private lateinit var getReceiptsUseCase: GetReceiptsUseCase
    private lateinit var getTypesAndCategoriesUseCase: GetTypesAndCategoriesUseCase
    private lateinit var viewModel: DashboardViewModel
    
    private val testDispatcher = StandardTestDispatcher()

    @BeforeTest
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        
        getReceiptsUseCase = mockk()
        getTypesAndCategoriesUseCase = mockk()
        
        mockkObject(DateUtils)
        
        coEvery { getTypesAndCategoriesUseCase.getCategories() } returns flowOf(emptyList())
        
        // Mock current date as Friday (2024-01-05)
        every { DateUtils.getCurrentTimestamp() } returns 1704456000000L // 2024-01-05 12:00:00
        every { DateUtils.timestampToLocalDate(1704456000000L) } returns LocalDate(2024, 1, 5) // Friday
        every { DateUtils.getStartOfWeek(any()) } returns 1704067200000L // 2024-01-01 00:00:00 (Monday)
        every { DateUtils.getEndOfDay(any()) } returns 1704456000000L
        every { DateUtils.getStartOfDay(any()) } returns 1704369600000L
        every { DateUtils.getYesterdayEnd() } returns 1704369599999L
        every { DateUtils.localDateToTimestamp(any()) } returns 1704456000000L
        every { DateUtils.isoStringToTimestamp(any()) } returns 1704456000000L
        every { DateUtils.timestampToIsoString(any()) } returns "2024-01-05T12:00:00Z"
        every { DateUtils.getCurrentWeekInfoCurrentWeek() } returns DateUtils.CurrentWeekInfoCurrentWeek(
            weekNumber = 1,
            days = listOf(
                DateUtils.DayItemCurrentWeek("pn", 1, "Styczeń", false, true, LocalDate(2024, 1, 1)),
                DateUtils.DayItemCurrentWeek("wt", 2, "Styczeń", false, false, LocalDate(2024, 1, 2)),
                DateUtils.DayItemCurrentWeek("śr", 3, "Styczeń", false, false, LocalDate(2024, 1, 3)),
                DateUtils.DayItemCurrentWeek("cz", 4, "Styczeń", false, false, LocalDate(2024, 1, 4)),
                DateUtils.DayItemCurrentWeek("pt", 5, "Styczeń", true, false, LocalDate(2024, 1, 5)),
                DateUtils.DayItemCurrentWeek("sb", 6, "Styczeń", false, false, LocalDate(2024, 1, 6)),
                DateUtils.DayItemCurrentWeek("nd", 7, "Styczeń", false, false, LocalDate(2024, 1, 7))
            )
        )
    }

    @AfterTest
    fun tearDown() {
        Dispatchers.resetMain()
        unmockkAll()
    }

    @Test
    fun `integration test - adaptive budget responds to day selection`() = runTest {
        // Given - Week with some spending
        val weekReceipts = listOf(
            createMockReceipt("2024-01-01T10:00:00Z", 10000L), // Monday: 100 zł
            createMockReceipt("2024-01-02T10:00:00Z", 15000L)  // Tuesday: 150 zł
        )

        coEvery { getReceiptsUseCase.getAllReceipts() } returns flowOf(weekReceipts)
        coEvery { getReceiptsUseCase.getReceiptsWithDateFilter(any(), any()) } returns flowOf(weekReceipts)

        viewModel = DashboardViewModel(getReceiptsUseCase, getTypesAndCategoriesUseCase)
        testDispatcher.scheduler.advanceUntilIdle()

        // When - Test different days through UI state

        // Test Monday
        viewModel.onDaySelected(LocalDate(2024, 1, 1))
        testDispatcher.scheduler.advanceUntilIdle()
        var uiState = viewModel.uiState.value
        assertNotNull(uiState.selectedDate)
        assertTrue(uiState.budget >= 0)

        // Test Friday (current day)
        viewModel.onDaySelected(LocalDate(2024, 1, 5))
        testDispatcher.scheduler.advanceUntilIdle()
        uiState = viewModel.uiState.value
        assertTrue(uiState.isAdaptiveBudgetActive)
        assertTrue(uiState.adaptiveDailyBudget >= 0)

        // Test Sunday
        viewModel.onDaySelected(LocalDate(2024, 1, 7))
        testDispatcher.scheduler.advanceUntilIdle()
        uiState = viewModel.uiState.value
        assertTrue(uiState.budget >= 0)
    }

    @Test
    fun `edge case - no spending in week shows base budgets`() = runTest {
        // Given - Empty week
        coEvery { getReceiptsUseCase.getAllReceipts() } returns flowOf(emptyList())
        coEvery { getReceiptsUseCase.getReceiptsWithDateFilter(any(), any()) } returns flowOf(emptyList())

        viewModel = DashboardViewModel(getReceiptsUseCase, getTypesAndCategoriesUseCase)
        testDispatcher.scheduler.advanceUntilIdle()

        // When & Then
        viewModel.onDaySelected(LocalDate(2024, 1, 1))
        testDispatcher.scheduler.advanceUntilIdle()
        var uiState = viewModel.uiState.value
        assertTrue(uiState.budget > 0) // Has some budget

        viewModel.onDaySelected(LocalDate(2024, 1, 5))
        testDispatcher.scheduler.advanceUntilIdle()
        uiState = viewModel.uiState.value
        assertTrue(uiState.budget > 0) // Has some budget
    }

    @Test
    fun `edge case - budget severely exceeded shows emergency budget`() = runTest {
        // Given - Massive overspending
        val weekReceipts = listOf(
            createMockReceipt("2024-01-01T10:00:00Z", 50000L), // Monday: 500 zł
            createMockReceipt("2024-01-02T10:00:00Z", 40000L), // Tuesday: 400 zł
            createMockReceipt("2024-01-03T10:00:00Z", 30000L), // Wednesday: 300 zł
            createMockReceipt("2024-01-04T10:00:00Z", 25000L)  // Thursday: 250 zł
        )

        coEvery { getReceiptsUseCase.getAllReceipts() } returns flowOf(weekReceipts)
        coEvery { getReceiptsUseCase.getReceiptsWithDateFilter(any(), any()) } returns flowOf(weekReceipts)

        viewModel = DashboardViewModel(getReceiptsUseCase, getTypesAndCategoriesUseCase)
        testDispatcher.scheduler.advanceUntilIdle()

        // When - Test current day (Friday) with massive overspending
        viewModel.onDaySelected(LocalDate(2024, 1, 5))
        testDispatcher.scheduler.advanceUntilIdle()

        // Then - Should show adaptive budget (emergency budget internally)
        val uiState = viewModel.uiState.value
        assertEquals(BudgetType.CURRENT_ADAPTIVE, uiState.budgetType)
        assertTrue(uiState.adaptiveDailyBudget > 0) // Should have some emergency budget
    }

    @Test
    fun `edge case - Sunday selection works`() = runTest {
        // Given
        val weekReceipts = listOf(
            createMockReceipt("2024-01-01T10:00:00Z", 12000L), // Monday: 120 zł
            createMockReceipt("2024-01-02T10:00:00Z", 11000L)  // Tuesday: 110 zł
        )

        coEvery { getReceiptsUseCase.getAllReceipts() } returns flowOf(weekReceipts)
        coEvery { getReceiptsUseCase.getReceiptsWithDateFilter(any(), any()) } returns flowOf(weekReceipts)

        viewModel = DashboardViewModel(getReceiptsUseCase, getTypesAndCategoriesUseCase)
        testDispatcher.scheduler.advanceUntilIdle()

        // When - Test Sunday selection
        viewModel.onDaySelected(LocalDate(2024, 1, 7))
        testDispatcher.scheduler.advanceUntilIdle()

        // Then
        val uiState = viewModel.uiState.value
        assertEquals(LocalDate(2024, 1, 7), uiState.selectedDate)
        assertTrue(uiState.budget >= 0) // Should have some budget
    }

    @Test
    fun `integration test - previous week summary is created`() = runTest {
        // Given - Previous week with some spending
        val previousWeekReceipts = listOf(
            createMockReceipt("2023-12-25T10:00:00Z", 5000L),   // Monday: 50 zł
            createMockReceipt("2023-12-26T10:00:00Z", 20000L),  // Tuesday: 200 zł
            createMockReceipt("2023-12-27T10:00:00Z", 15000L)   // Wednesday: 150 zł
        )

        coEvery { getReceiptsUseCase.getAllReceipts() } returns flowOf(emptyList())
        coEvery { getReceiptsUseCase.getReceiptsWithDateFilter(any(), any()) } returns flowOf(previousWeekReceipts)

        viewModel = DashboardViewModel(getReceiptsUseCase, getTypesAndCategoriesUseCase)
        testDispatcher.scheduler.advanceUntilIdle()

        // When
        val uiState = viewModel.uiState.value

        // Then
        assertNotNull(uiState.previousWeekSummary)
        val summary = uiState.previousWeekSummary!!

        // Verify basic structure
        assertTrue(summary.totalSpending >= 0)
        assertTrue(summary.totalBudget > 0)
        assertTrue(summary.weekBudgetUsagePercent >= 0f)
        assertTrue(summary.weekRange.isNotEmpty())

        // Verify daily breakdown
        assertEquals(7, summary.dailySpendings.size)

        // All days should have valid data
        summary.dailySpendings.forEach { daySpending ->
            assertTrue(daySpending.dayName.isNotEmpty())
            assertTrue(daySpending.date > 0)
            assertTrue(daySpending.month.isNotEmpty())
            assertTrue(daySpending.spending >= 0)
            assertTrue(daySpending.budget > 0)
        }
    }

    @Test
    fun `budget progress calculation works with adaptive budget`() = runTest {
        // Given
        val weekReceipts = listOf(
            createMockReceipt("2024-01-01T10:00:00Z", 10000L), // Monday: 100 zł
            createMockReceipt("2024-01-02T10:00:00Z", 15000L)  // Tuesday: 150 zł
        )

        coEvery { getReceiptsUseCase.getAllReceipts() } returns flowOf(weekReceipts)
        coEvery { getReceiptsUseCase.getReceiptsWithDateFilter(any(), any()) } returns flowOf(weekReceipts)

        viewModel = DashboardViewModel(getReceiptsUseCase, getTypesAndCategoriesUseCase)
        testDispatcher.scheduler.advanceUntilIdle()

        // When - Select Friday (current day)
        viewModel.onDaySelected(LocalDate(2024, 1, 5))
        testDispatcher.scheduler.advanceUntilIdle()

        // Then
        val uiState = viewModel.uiState.value

        // Verify adaptive budget is active
        assertTrue(uiState.isAdaptiveBudgetActive)
        assertTrue(uiState.adaptiveDailyBudget >= 0)

        // Verify budget progress details exist
        assertNotNull(uiState.budgetProgressDetails)

        // Verify spending recommendation exists
        assertNotNull(uiState.spendingRecommendation)
    }

    private fun createMockReceipt(purchaseDate: String, receiptSum: Long): Receipt {
        return Receipt(
            id = "test-id",
            name = "Test Store",
            products = emptyList(),
            saveDate = purchaseDate,
            purchaseDate = purchaseDate,
            receiptSum = receiptSum,
            purchaseMethod = "Card",
            productIds = emptyList(),
            imagePath = null
        )
    }
}

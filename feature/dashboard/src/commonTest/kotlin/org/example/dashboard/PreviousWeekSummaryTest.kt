package org.example.dashboard

import kotlinx.datetime.LocalDate
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class PreviousWeekSummaryTest {

    @Test
    fun `PreviousWeekSummary data class - normal week under budget`() {
        // Given
        val dailySpendings = createDailySpendings(
            listOf(10000L, 12000L, 8000L, 15000L, 9000L, 11000L, 7000L) // Total: 72000L (720 zł)
        )
        
        // When
        val summary = PreviousWeekSummary(
            weekRange = "01.01 - 07.01",
            totalSpending = 72000L,
            totalBudget = 95700L,
            weekBudgetUsagePercent = 75.2f,
            isOverBudget = false,
            dailySpendings = dailySpendings
        )
        
        // Then
        assertEquals("01.01 - 07.01", summary.weekRange)
        assertEquals(72000L, summary.totalSpending)
        assertEquals(95700L, summary.totalBudget)
        assertEquals(75.2f, summary.weekBudgetUsagePercent)
        assertFalse(summary.isOverBudget)
        assertEquals(7, summary.dailySpendings.size)
    }

    @Test
    fun `PreviousWeekSummary data class - week over budget`() {
        // Given
        val dailySpendings = createDailySpendings(
            listOf(20000L, 18000L, 22000L, 17000L, 19000L, 21000L, 16000L) // Total: 133000L (1330 zł)
        )
        
        // When
        val summary = PreviousWeekSummary(
            weekRange = "08.01 - 14.01",
            totalSpending = 133000L,
            totalBudget = 95700L,
            weekBudgetUsagePercent = 139.0f,
            isOverBudget = true,
            dailySpendings = dailySpendings
        )
        
        // Then
        assertEquals("08.01 - 14.01", summary.weekRange)
        assertEquals(133000L, summary.totalSpending)
        assertTrue(summary.isOverBudget)
        assertTrue(summary.weekBudgetUsagePercent > 100f)
    }

    @Test
    fun `DaySpending data class - day under budget`() {
        // Given & When
        val daySpending = DaySpending(
            dayName = "Pon",
            date = 1,
            month = "Sty",
            localDate = LocalDate(2024, 1, 1),
            spending = 10000L, // 100 zł
            budget = 13671L,   // ~136.71 zł
            isOverBudget = false,
            budgetUsagePercent = 73.1f
        )
        
        // Then
        assertEquals("Pon", daySpending.dayName)
        assertEquals(1, daySpending.date)
        assertEquals("Sty", daySpending.month)
        assertEquals(10000L, daySpending.spending)
        assertEquals(13671L, daySpending.budget)
        assertFalse(daySpending.isOverBudget)
        assertTrue(daySpending.budgetUsagePercent < 80f) // Under 80% threshold
    }

    @Test
    fun `DaySpending data class - day over budget`() {
        // Given & When
        val daySpending = DaySpending(
            dayName = "Wto",
            date = 2,
            month = "Sty",
            localDate = LocalDate(2024, 1, 2),
            spending = 20000L, // 200 zł
            budget = 13671L,   // ~136.71 zł
            isOverBudget = true,
            budgetUsagePercent = 146.3f
        )
        
        // Then
        assertEquals("Wto", daySpending.dayName)
        assertEquals(2, daySpending.date)
        assertEquals("Sty", daySpending.month)
        assertEquals(20000L, daySpending.spending)
        assertTrue(daySpending.isOverBudget)
        assertTrue(daySpending.budgetUsagePercent > 100f)
    }

    @Test
    fun `DaySpending data class - day with no spending`() {
        // Given & When
        val daySpending = DaySpending(
            dayName = "Śro",
            date = 3,
            month = "Sty",
            localDate = LocalDate(2024, 1, 3),
            spending = 0L,     // No spending
            budget = 13671L,   // ~136.71 zł
            isOverBudget = false,
            budgetUsagePercent = 0f
        )
        
        // Then
        assertEquals("Śro", daySpending.dayName)
        assertEquals(0L, daySpending.spending)
        assertFalse(daySpending.isOverBudget)
        assertEquals(0f, daySpending.budgetUsagePercent)
    }

    @Test
    fun `DaySpending data class - day near budget limit`() {
        // Given & When
        val daySpending = DaySpending(
            dayName = "Czw",
            date = 4,
            month = "Sty",
            localDate = LocalDate(2024, 1, 4),
            spending = 12000L, // 120 zł
            budget = 13671L,   // ~136.71 zł
            isOverBudget = false,
            budgetUsagePercent = 87.8f // Near limit but under budget
        )
        
        // Then
        assertEquals("Czw", daySpending.dayName)
        assertEquals(12000L, daySpending.spending)
        assertFalse(daySpending.isOverBudget)
        assertTrue(daySpending.budgetUsagePercent > 80f) // Over 80% threshold
        assertTrue(daySpending.budgetUsagePercent < 100f) // But under budget
    }

    @Test
    fun `week range formatting validation`() {
        // Given
        val testCases = listOf(
            "01.01 - 07.01" to "First week of January",
            "25.12 - 31.12" to "Last week of December",
            "29.02 - 06.03" to "Week spanning February-March (leap year)",
            "28.02 - 06.03" to "Week spanning February-March (non-leap year)"
        )
        
        // When & Then
        testCases.forEach { (weekRange, description) ->
            val summary = PreviousWeekSummary(
                weekRange = weekRange,
                totalSpending = 50000L,
                totalBudget = 95700L,
                weekBudgetUsagePercent = 52.2f,
                isOverBudget = false,
                dailySpendings = emptyList()
            )
            
            assertEquals(weekRange, summary.weekRange, "Failed for: $description")
            assertTrue(weekRange.contains(" - "), "Week range should contain ' - ' separator")
            assertTrue(weekRange.matches(Regex("\\d{2}\\.\\d{2} - \\d{2}\\.\\d{2}")), 
                "Week range should match DD.MM - DD.MM format")
        }
    }

    @Test
    fun `budget usage percentage calculations`() {
        // Given
        val testCases = listOf(
            Triple(47850L, 95700L, 50.0f),    // Exactly 50%
            Triple(76560L, 95700L, 80.0f),    // Exactly 80%
            Triple(95700L, 95700L, 100.0f),   // Exactly 100%
            Triple(115000L, 95700L, 120.2f),  // Over budget
            Triple(0L, 95700L, 0.0f)          // No spending
        )
        
        // When & Then
        testCases.forEach { (spending, budget, expectedPercent) ->
            val actualPercent = if (budget > 0) (spending.toFloat() / budget) * 100f else 0f
            assertEquals(expectedPercent, actualPercent, 0.1f, 
                "Failed for spending: $spending, budget: $budget")
        }
    }

    @Test
    fun `daily spending status indicators`() {
        // Given
        val testCases = listOf(
            Triple(5000L, 13671L, "under_80_percent"),    // 36.6% - Green (TrendingDown)
            Triple(11000L, 13671L, "near_limit"),         // 80.5% - Amber (CheckCircle)
            Triple(15000L, 13671L, "over_budget")         // 109.7% - Red (Warning)
        )
        
        // When & Then
        testCases.forEach { (spending, budget, expectedStatus) ->
            val budgetUsagePercent = (spending.toFloat() / budget) * 100f
            val isOverBudget = spending > budget
            
            when (expectedStatus) {
                "under_80_percent" -> {
                    assertFalse(isOverBudget)
                    assertTrue(budgetUsagePercent < 80f)
                }
                "near_limit" -> {
                    assertFalse(isOverBudget)
                    assertTrue(budgetUsagePercent >= 80f)
                }
                "over_budget" -> {
                    assertTrue(isOverBudget)
                    assertTrue(budgetUsagePercent > 100f)
                }
            }
        }
    }

    private fun createDailySpendings(amounts: List<Long>): List<DaySpending> {
        val dayNames = listOf("Pon", "Wto", "Śro", "Czw", "Pią", "Sob", "Nie")
        val monthNames = listOf("Sty", "Lut", "Mar", "Kwi", "Maj", "Cze", "Lip", "Sie", "Wrz", "Paź", "Lis", "Gru")
        
        return amounts.mapIndexed { index, amount ->
            val date = index + 1
            val budget = 13671L // DAILY_BASE_BUDGET
            val budgetUsagePercent = if (budget > 0) (amount.toFloat() / budget) * 100f else 0f
            
            DaySpending(
                dayName = dayNames[index],
                date = date,
                month = monthNames[0], // January
                localDate = LocalDate(2024, 1, date),
                spending = amount,
                budget = budget,
                isOverBudget = amount > budget,
                budgetUsagePercent = budgetUsagePercent
            )
        }
    }
}

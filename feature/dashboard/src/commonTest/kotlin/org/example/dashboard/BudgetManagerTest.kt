package org.example.dashboard

import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.test.assertFalse

class BudgetManagerTest {

    companion object {
        private const val WEEKLY_BUDGET = 95700L // 957,00 zł
        private const val DAILY_BASE_BUDGET = WEEKLY_BUDGET / 7 // ~136,71 zł
    }

    @Test
    fun `calculateAdaptiveBudgetForSpecificDay - Monday current day returns current adaptive budget`() {
        // Given
        val targetDayOfWeek = 1 // Poniedziałek
        val currentDayOfWeek = 1 // Dzisiaj jest poniedziałek
        val weeklySpentUpToTargetDay = 0L
        val weeklySpentUpToPreviousDay = 0L

        // When
        val result = BudgetManager.calculateAdaptiveBudgetForSpecificDay(
            targetDayOfWeek = targetDayOfWeek,
            currentDayOfWeek = currentDayOfWeek,
            weeklySpentUpToTargetDay = weeklySpentUpToTargetDay,
            weeklySpentUpToPreviousDay = weeklySpentUpToPreviousDay
        )

        // Then
        assertEquals(BudgetType.CURRENT_ADAPTIVE, result.type)
        assertEquals(DAILY_BASE_BUDGET, result.budget)
    }

    @Test
    fun `calculateAdaptiveBudgetForSpecificDay - Wednesday current day with previous spending`() {
        // Given
        val targetDayOfWeek = 3 // Środa
        val currentDayOfWeek = 3 // Dzisiaj jest środa
        val weeklySpentUpToPreviousDay = 20000L // 200 zł wydane w pon-wt
        val weeklySpentUpToTargetDay = 25000L // 250 zł wydane w pon-śr

        // When
        val result = BudgetManager.calculateAdaptiveBudgetForSpecificDay(
            targetDayOfWeek = targetDayOfWeek,
            currentDayOfWeek = currentDayOfWeek,
            weeklySpentUpToTargetDay = weeklySpentUpToTargetDay,
            weeklySpentUpToPreviousDay = weeklySpentUpToPreviousDay
        )

        // Then
        assertEquals(BudgetType.CURRENT_ADAPTIVE, result.type)
        // Pozostały budżet: 95700 - 20000 = 75700
        // Pozostałe dni: 8 - 3 = 5 dni
        // Adaptacyjny budżet: 75700 / 5 = 15140
        assertEquals(15140L, result.budget)
    }

    @Test
    fun `calculateAdaptiveBudgetForSpecificDay - past day returns retrospective budget`() {
        // Given
        val targetDayOfWeek = 2 // Wtorek (przeszły)
        val currentDayOfWeek = 4 // Dzisiaj jest czwartek
        val weeklySpentUpToTargetDay = 15000L // 150 zł wydane w pon-wt
        val weeklySpentUpToPreviousDay = 10000L // 100 zł wydane w pon

        // When
        val result = BudgetManager.calculateAdaptiveBudgetForSpecificDay(
            targetDayOfWeek = targetDayOfWeek,
            currentDayOfWeek = currentDayOfWeek,
            weeklySpentUpToTargetDay = weeklySpentUpToTargetDay,
            weeklySpentUpToPreviousDay = weeklySpentUpToPreviousDay
        )

        // Then
        assertEquals(BudgetType.RETROSPECTIVE, result.type)
        // Retrospektywny budżet dla wtorku: adaptacyjny budżet jaki był we wtorek
        // Pozostały budżet po poniedziałku: 95700 - 10000 = 85700
        // Pozostałe dni od wtorku: 8 - 2 = 6 dni
        // Adaptacyjny budżet na wtorek: 85700 / 6 = 14283
        assertEquals(14283L, result.budget)
    }

    @Test
    fun `calculateAdaptiveBudgetForSpecificDay - future day returns predictive budget`() {
        // Given
        val targetDayOfWeek = 6 // Sobota (przyszły)
        val currentDayOfWeek = 4 // Dzisiaj jest czwartek
        val weeklySpentUpToTargetDay = 0L // Nie używane dla przyszłych dni
        val weeklySpentUpToPreviousDay = 40000L // 400 zł wydane w pon-śr

        // When
        val result = BudgetManager.calculateAdaptiveBudgetForSpecificDay(
            targetDayOfWeek = targetDayOfWeek,
            currentDayOfWeek = currentDayOfWeek,
            weeklySpentUpToTargetDay = weeklySpentUpToTargetDay,
            weeklySpentUpToPreviousDay = weeklySpentUpToPreviousDay
        )

        // Then
        assertEquals(BudgetType.PREDICTIVE, result.type)
        // Pozostały budżet od dzisiaj: 95700 - 40000 = 55700
        // Pozostałe dni od dzisiaj: 8 - 4 = 4 dni
        // Pozostałe dni bez dnia docelowego: 4 - 1 = 3 dni
        // Przewidywany budżet: 55700 / 3 = 18566
        assertEquals(18566L, result.budget)
    }

    @Test
    fun `calculateAdaptiveBudgetForSpecificDay - budget exceeded returns emergency budget`() {
        // Given
        val targetDayOfWeek = 5 // Piątek
        val currentDayOfWeek = 5 // Dzisiaj jest piątek
        val weeklySpentUpToPreviousDay = 100000L // 1000 zł wydane (przekroczenie)
        val weeklySpentUpToTargetDay = 105000L

        // When
        val result = BudgetManager.calculateAdaptiveBudgetForSpecificDay(
            targetDayOfWeek = targetDayOfWeek,
            currentDayOfWeek = currentDayOfWeek,
            weeklySpentUpToTargetDay = weeklySpentUpToTargetDay,
            weeklySpentUpToPreviousDay = weeklySpentUpToPreviousDay
        )

        // Then
        assertEquals(BudgetType.CURRENT_ADAPTIVE, result.type)
        // Emergency budget: DAILY_BASE_BUDGET / 4 = 13671 / 4 = 3417
        assertEquals(DAILY_BASE_BUDGET / 4, result.budget)
    }

    @Test
    fun `calculateAdaptiveBudgetForSpecificDay - Sunday last day of week`() {
        // Given
        val targetDayOfWeek = 7 // Niedziela
        val currentDayOfWeek = 7 // Dzisiaj jest niedziela
        val weeklySpentUpToPreviousDay = 80000L // 800 zł wydane w pon-sob
        val weeklySpentUpToTargetDay = 85000L

        // When
        val result = BudgetManager.calculateAdaptiveBudgetForSpecificDay(
            targetDayOfWeek = targetDayOfWeek,
            currentDayOfWeek = currentDayOfWeek,
            weeklySpentUpToTargetDay = weeklySpentUpToTargetDay,
            weeklySpentUpToPreviousDay = weeklySpentUpToPreviousDay
        )

        // Then
        assertEquals(BudgetType.CURRENT_ADAPTIVE, result.type)
        // Pozostały budżet: 95700 - 80000 = 15700
        // Pozostałe dni: 8 - 7 = 1 dzień
        // Adaptacyjny budżet: 15700 / 1 = 15700
        assertEquals(15700L, result.budget)
    }

    @Test
    fun `calculateAdaptiveDailyBudget - Monday returns base budget`() {
        // Given
        val weeklySpentSoFar = 0L
        val dayOfWeek = 1 // Poniedziałek
        val weeklyBudget = WEEKLY_BUDGET

        // When
        val result = BudgetManager.calculateAdaptiveDailyBudget(
            weeklySpentSoFar = weeklySpentSoFar,
            dayOfWeek = dayOfWeek,
            weeklyBudget = weeklyBudget
        )

        // Then
        assertEquals(DAILY_BASE_BUDGET, result)
    }

    @Test
    fun `calculateAdaptiveDailyBudget - mid week with normal spending`() {
        // Given
        val weeklySpentSoFar = 30000L // 300 zł wydane w pon-wt
        val dayOfWeek = 3 // Środa
        val weeklyBudget = WEEKLY_BUDGET

        // When
        val result = BudgetManager.calculateAdaptiveDailyBudget(
            weeklySpentSoFar = weeklySpentSoFar,
            dayOfWeek = dayOfWeek,
            weeklyBudget = weeklyBudget
        )

        // Then
        // Pozostały budżet: 95700 - 30000 = 65700
        // Pozostałe dni: 8 - 3 = 5 dni
        // Adaptacyjny budżet: 65700 / 5 = 13140
        assertEquals(13140L, result)
    }

    @Test
    fun `calculateAdaptiveDailyBudget - budget exceeded returns emergency budget`() {
        // Given
        val weeklySpentSoFar = 100000L // 1000 zł wydane (przekroczenie)
        val dayOfWeek = 4 // Czwartek
        val weeklyBudget = WEEKLY_BUDGET

        // When
        val result = BudgetManager.calculateAdaptiveDailyBudget(
            weeklySpentSoFar = weeklySpentSoFar,
            dayOfWeek = dayOfWeek,
            weeklyBudget = weeklyBudget
        )

        // Then
        // Emergency budget: DAILY_BASE_BUDGET / 4
        assertEquals(DAILY_BASE_BUDGET / 4, result)
    }

    @Test
    fun `calculateAdaptiveDailyBudget - end of week returns zero`() {
        // Given
        val weeklySpentSoFar = 50000L
        val dayOfWeek = 8 // Po niedzieli (nie powinno się zdarzyć, ale test edge case)
        val weeklyBudget = WEEKLY_BUDGET

        // When
        val result = BudgetManager.calculateAdaptiveDailyBudget(
            weeklySpentSoFar = weeklySpentSoFar,
            dayOfWeek = dayOfWeek,
            weeklyBudget = weeklyBudget
        )

        // Then
        assertEquals(0L, result)
    }

    @Test
    fun `calculateBudgetProgress - normal spending within budget`() {
        // Given
        val currentSpending = 10000L // 100 zł
        val originalBudget = 15000L // 150 zł
        val adaptiveBudget = 12000L // 120 zł

        // When
        val result = BudgetManager.calculateBudgetProgress(
            currentSpending = currentSpending,
            originalBudget = originalBudget,
            adaptiveBudget = adaptiveBudget
        )

        // Then
        assertEquals(10000f / 12000f, result.progress, 0.01f)
        assertFalse(result.isExceeded)
        assertTrue(result.isNearLimit) // 83% > 80%
        assertEquals(2000L, result.remainingBudget)
        assertEquals(10000L, result.usedBudget)
        assertEquals(12000L, result.totalBudget)
    }

    @Test
    fun `calculateBudgetProgress - spending exceeded budget`() {
        // Given
        val currentSpending = 15000L // 150 zł
        val originalBudget = 10000L // 100 zł
        val adaptiveBudget = 12000L // 120 zł

        // When
        val result = BudgetManager.calculateBudgetProgress(
            currentSpending = currentSpending,
            originalBudget = originalBudget,
            adaptiveBudget = adaptiveBudget
        )

        // Then
        assertEquals(15000f / 12000f, result.progress, 0.01f)
        assertTrue(result.isExceeded)
        assertTrue(result.isNearLimit)
        assertEquals(-3000L, result.remainingBudget)
        assertEquals(15000L, result.usedBudget)
        assertEquals(12000L, result.totalBudget)
    }

    @Test
    fun `calculateRecommendedSpending - on track spending`() {
        // Given
        val currentDaySpent = 5000L // 50 zł
        val adaptiveDailyBudget = 12000L // 120 zł
        val timeOfDay = 0.5f // Połowa dnia

        // When
        val result = BudgetManager.calculateRecommendedSpending(
            currentDaySpent = currentDaySpent,
            adaptiveDailyBudget = adaptiveDailyBudget,
            timeOfDay = timeOfDay
        )

        // Then
        assertEquals(7000L, result.recommendedRemaining) // 120 - 50 = 70 zł
        assertTrue(result.isOnTrack) // 50 <= 60 (50% z 120)
        assertEquals(-1000L, result.variance) // 50 - 60 = -10 zł (oszczędność)
    }

    @Test
    fun `calculateRecommendedSpending - overspending`() {
        // Given
        val currentDaySpent = 10000L // 100 zł
        val adaptiveDailyBudget = 12000L // 120 zł
        val timeOfDay = 0.5f // Połowa dnia

        // When
        val result = BudgetManager.calculateRecommendedSpending(
            currentDaySpent = currentDaySpent,
            adaptiveDailyBudget = adaptiveDailyBudget,
            timeOfDay = timeOfDay
        )

        // Then
        assertEquals(2000L, result.recommendedRemaining) // 120 - 100 = 20 zł
        assertFalse(result.isOnTrack) // 100 > 60 (50% z 120)
        assertEquals(4000L, result.variance) // 100 - 60 = 40 zł (przekroczenie)
    }
}

package org.example.dashboard.component


import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.SizeTransform
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.VectorConverter
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.BarChart
import androidx.compose.material.icons.rounded.GridView
import androidx.compose.material.icons.rounded.Remove
import androidx.compose.material.icons.rounded.TrendingUp
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay
import org.example.dashboard.DaySpending
import org.example.dashboard.PreviousWeekSummary
import org.example.dashboard.formatPrice

@Composable
fun PreviousWeekSummary(
    previousWeekSummary: PreviousWeekSummary?,
    modifier: Modifier = Modifier
) {
    if (previousWeekSummary == null) return

    /** Using rememberSaveable because with  remember isBarView is not saved when component is removed from the screen - it caused resetting the view to bar chart
     * * var isBarView by remember { mutableStateOf(true) }
     * */
    var isBarView by rememberSaveable { mutableStateOf(true) }


    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "Poprzedni tydzień",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = previousWeekSummary.weekRange,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // Overall status indicator
            val statusColor = when {
                previousWeekSummary.isOverBudget -> Color(0xFFEF4444) // Red
                previousWeekSummary.weekBudgetUsagePercent < 80f -> Color(0xFF10B981) // Green
                else -> Color(0xFFF59E0B) // Amber
            }

            Box(
                modifier = Modifier
                    .size(12.dp)
                    .background(statusColor, CircleShape)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Total spending vs budget
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.Bottom
        ) {
            Text(
                text = formatPrice(previousWeekSummary.totalSpending),
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Text(
                text = "${previousWeekSummary.weekBudgetUsagePercent.toInt()}%",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = if (previousWeekSummary.isOverBudget)
                    Color(0xFFEF4444) else Color(0xFF10B981)
            )
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "z ${formatPrice(previousWeekSummary.totalBudget)}",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Spacer(modifier = Modifier.height(12.dp))

        // Progress bar
        LinearProgressIndicator(
            progress = { (previousWeekSummary.weekBudgetUsagePercent / 100f).coerceAtMost(1f) },
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .clip(RoundedCornerShape(4.dp)),
            color = if (previousWeekSummary.isOverBudget) Color(0xFFEF4444) else Color(
                0xFF10B981
            ),
            trackColor = Color(0xFFE5E7EB),
        )

        Spacer(modifier = Modifier.height(20.dp))

        // Daily breakdown header with toggle
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Wydatki dzienne",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )

            // Toggle button
            IconButton(
                onClick = { isBarView = !isBarView },
                modifier = Modifier.size(32.dp)
            ) {
                // NOWOŚĆ: Animowana ikona
                AnimatedContent(
                    targetState = isBarView,
                    transitionSpec = {
                        if (targetState) {
                            (slideInVertically { height -> height } + fadeIn()) togetherWith
                                    (slideOutVertically { height -> -height } + fadeOut())
                        } else {
                            (slideInVertically { height -> -height } + fadeIn()) togetherWith
                                    (slideOutVertically { height -> height } + fadeOut())
                        }.using(
                            SizeTransform(clip = false)
                        )
                    },
                    label = "Toggle Icon Animation"
                ) { isBarViewTarget ->
                    Icon(
                        imageVector = if (isBarViewTarget) Icons.Rounded.GridView else Icons.Rounded.BarChart,
                        contentDescription = if (isBarViewTarget) "Przełącz na kafelki" else "Przełącz na słupki",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(18.dp)
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(12.dp))

        // NOWOŚĆ: Ulepszony AnimatedContent
        AnimatedContent(
            targetState = isBarView,
            label = "View Switch Animation",
            transitionSpec = {
                fadeIn(animationSpec = tween(220, delayMillis = 90)) +
                        slideInVertically(
                            initialOffsetY = { height -> height / 5 },
                            animationSpec = tween(220, delayMillis = 90)
                        ) togetherWith
                        fadeOut(animationSpec = tween(90))
            }
        ) { targetIsBarView ->
            if (targetIsBarView) {
                DailySpendingsBars(
                    dailySpendings = previousWeekSummary.dailySpendings,
                    modifier = Modifier.fillMaxWidth()
                )
            } else {
                DailySpendingsGrid(
                    dailySpendings = previousWeekSummary.dailySpendings,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        QuickStatsSummary(
            previousWeekSummary = previousWeekSummary,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
private fun DailySpendingsBars(
    dailySpendings: List<DaySpending>,
    modifier: Modifier = Modifier
) {
    val maxBarHeight = 60.dp

    Row(
        modifier = modifier.height(80.dp),
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        verticalAlignment = Alignment.Bottom
    ) {
        dailySpendings.forEachIndexed { index, daySpending ->
            // Definiujemy docelową wysokość dla słupka
            val targetHeight = (daySpending.budgetUsagePercent / 100f * maxBarHeight.value).dp
                .coerceAtMost(maxBarHeight)

            // NOWOŚĆ: Używamy Animatable do pełnej kontroli nad animacją
            val animatedHeight =
                remember { Animatable(initialValue = 0.dp, typeConverter = Dp.VectorConverter) }

            // NOWOŚĆ: Używamy LaunchedEffect do uruchomienia animacji z opóźnieniem
            LaunchedEffect(key1 = targetHeight) {
                // Opóźnienie startu animacji dla każdego kolejnego słupka
                delay(index * 120L) // Możesz dostosować opóźnienie (w milisekundach)

                animatedHeight.animateTo(
                    targetValue = targetHeight,
                    // Definiujemy fizykę animacji - efekt "sprężyny"
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioLowBouncy,
                        stiffness = Spring.StiffnessLow
                    )
                )
            }

            Column(
                modifier = Modifier.weight(1f),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Bottom
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(maxBarHeight),
                    contentAlignment = Alignment.BottomCenter
                ) {
                    // Tło słupka
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(4.dp)
                            .background(Color(0xFFE5E7EB), RoundedCornerShape(2.dp))
                    )

                    // Słupek z animowaną wysokością
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(animatedHeight.value) // ZMIANA: Używamy wartości z Animatable
                            .background(
                                when {
                                    daySpending.isOverBudget -> Color(0xFFEF4444)
                                    daySpending.budgetUsagePercent > 50f -> Color(0xFFF59E0B)
                                    else -> Color(0xFF10B981)
                                },
                                RoundedCornerShape(
                                    topStart = 4.dp,
                                    topEnd = 4.dp,
                                    bottomStart = 2.dp,
                                    bottomEnd = 2.dp
                                )
                            )
                    )
                }

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = daySpending.dayName.take(2).lowercase(),
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
fun AnimatedGridItem(
    delay: Int,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    var isVisible by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        delay(delay.toLong())
        isVisible = true
    }

    val animatedAlpha by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0f,
        animationSpec = tween(durationMillis = 300),
        label = "Grid Item Alpha"
    )

    val animatedScale by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0.8f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "Grid Item Scale"
    )

    Box(
        modifier = modifier
            .graphicsLayer {
                alpha = animatedAlpha
                scaleX = animatedScale
                scaleY = animatedScale
            }
    ) {
        content()
    }
}


@Composable
private fun DailySpendingsGrid(
    dailySpendings: List<DaySpending>,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // First row
        Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
            dailySpendings.take(3).forEachIndexed { index, daySpending ->
                AnimatedGridItem(delay = index * 50, modifier = Modifier.weight(1f)) {
                    CompactDaySpendingCard(
                        daySpending = daySpending,
                        modifier = Modifier.fillMaxWidth().weight(1f)
                    )
                }
            }
        }

        // Second row
        if (dailySpendings.size > 3) {
            Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                dailySpendings.drop(3).take(2).forEachIndexed { index, daySpending ->
                    AnimatedGridItem(delay = (index + 3) * 50, modifier = Modifier.fillMaxWidth().weight(1f)) {
                        CompactDaySpendingCard(
                            daySpending = daySpending,
                            modifier = Modifier.fillMaxWidth().weight(1f)
                        )
                    }
                }
            }
        }

        // Third row
        if (dailySpendings.size > 5) {
            Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                dailySpendings.drop(5).forEachIndexed { index, daySpending ->
                    AnimatedGridItem(delay = (index + 5) * 50, modifier = Modifier.weight(1f)) {
                        CompactDaySpendingCard(
                            daySpending = daySpending,
                            modifier = Modifier.fillMaxWidth().weight(1f)
                        )
                    }
                }
//                Spacer(modifier = Modifier.weight(1f))
            }
        }
    }
}


@Composable
private fun CompactDaySpendingCard(
    daySpending: DaySpending,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF9FAFB)),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = daySpending.dayName,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                val (icon, iconColor) = when {
                    daySpending.budgetUsagePercent > 100f -> Icons.Rounded.TrendingUp to Color(
                        0xFFEF4444
                    )

                    daySpending.budgetUsagePercent > 50f -> Icons.Rounded.TrendingUp to Color(
                        0xFFF59E0B
                    )

                    else -> Icons.Rounded.Remove to Color(0xFF10B981)
                }
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = iconColor,
                    modifier = Modifier.size(12.dp)
                )
            }
            Text(
                text = formatPrice(daySpending.spending),
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onSurface
            )
            val percentageColor = when {
                daySpending.budgetUsagePercent > 100f -> Color(0xFFEF4444)
                daySpending.budgetUsagePercent > 50f -> Color(0xFFF59E0B)
                else -> Color(0xFF10B981)
            }
            Text(
                text = "${daySpending.budgetUsagePercent.toInt()}%",
                fontSize = 12.sp,
                color = percentageColor
            )
            LinearProgressIndicator(
                progress = { (daySpending.budgetUsagePercent / 200f).coerceAtMost(1f) },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(4.dp)
                    .clip(RoundedCornerShape(2.dp)),
                color = percentageColor,
                trackColor = Color.White,
            )
        }
    }
}

@Composable
private fun QuickStatsSummary(
    previousWeekSummary: PreviousWeekSummary,
    modifier: Modifier = Modifier
) {
    val highestDay = previousWeekSummary.dailySpendings.maxByOrNull { it.budgetUsagePercent }
    val daysOverBudget = previousWeekSummary.dailySpendings.count { it.isOverBudget }

    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = Color.Transparent),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 12.dp)
                .background(
                    color = Color(0xFFF9FAFB),
                    shape = RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp)
                )
                .padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "Najwyższy: ${highestDay?.dayName ?: "N/A"} (${highestDay?.budgetUsagePercent?.toInt() ?: 0}%)",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Text(
                    text = "Dni powyżej planu: $daysOverBudget",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}
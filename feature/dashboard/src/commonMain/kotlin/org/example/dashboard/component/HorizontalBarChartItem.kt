package org.example.dashboard

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import org.example.core.domain.model.Category

@Composable
fun HorizontalBarChartItem(
    categorySpending: CategorySpending,
    totalSpending: Long,
    availableCategories: List<Category>,
    index: Int,
    modifier: Modifier = Modifier
) {
    val fallbackColors = listOf(
        Color(0xFF6366F1), Color(0xFF8B5CF6), Color(0xFFEC4899), Color(0xFFF59E0B),
        Color(0xFF10B981), Color(0xFF3B82F6), Color(0xFFEF4444), Color(0xFF84CC16),
        Color(0xFF06B6D4), Color(0xFFF97316)
    )

    fun getCategoryColor(categoryName: String, index: Int): Color {
        val category = availableCategories.find { it.name == categoryName }
        return category?.let { Color(it.color) } ?: fallbackColors[index % fallbackColors.size]
    }

    val fraction = if (totalSpending > 0)
        categorySpending.categorySum.toFloat() / totalSpending.toFloat()
    else 0f

    Column(modifier = modifier.fillMaxWidth()) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = "${categorySpending.categoryName} -- ${fraction.times(100).toInt()} %",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.weight(1f)
            )

            Text(
                text = "${categorySpending.categorySum / 100}zł",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(12.dp)
                .clip(RoundedCornerShape(6.dp))
                .background(MaterialTheme.colorScheme.surfaceVariant)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(fraction)
                    .fillMaxHeight()
                    .background(getCategoryColor(categorySpending.categoryName, index))
            )
        }
    }
}
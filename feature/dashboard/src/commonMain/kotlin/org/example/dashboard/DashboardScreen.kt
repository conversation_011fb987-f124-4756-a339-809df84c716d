package org.example.dashboard


import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import org.example.dashboard.component.CurrentWeekBudget
import org.example.dashboard.component.PreviousWeekSummary
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun DashboardScreen() {
    val viewModel = koinViewModel<DashboardViewModel>()
    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                item {
                    CurrentWeekBudget(
                        selectedDate = uiState.selectedDate,
                        onDaySelected = viewModel::onDaySelected,
                        budget = uiState.budget,
                        spent = uiState.totalSpending,
                        // Nowe parametry
                        adaptiveDailyBudget = uiState.adaptiveDailyBudget,
                        budgetProgressDetails = uiState.budgetProgressDetails,
                        spendingRecommendation = uiState.spendingRecommendation,
                        isAdaptiveBudgetActive = uiState.isAdaptiveBudgetActive,
                        budgetType = uiState.budgetType,
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                }

                item {
                    HorizontalDivider(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp),
                        thickness = 1.dp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.1f)
                    )
                }

                item {
                    PreviousWeekSummary(
                        previousWeekSummary = uiState.previousWeekSummary,
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )
                    Spacer(modifier = Modifier.height(24.dp))
                }

                item {
                    HorizontalDivider(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp),
                        thickness = 1.dp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.1f)
                    )
                }

                item {
                    DonutChart(
                        totalSpending = uiState.totalSpending,
                        categorySpendings = uiState.categorySpendings,
                        availableCategories = uiState.availableCategories,
                    )
                    Spacer(modifier = Modifier.height(24.dp))
                }


                val sortedSpendings =
                    uiState.categorySpendings.sortedByDescending { it.categorySum }
                itemsIndexed(sortedSpendings) { index, categorySpending ->
                    HorizontalBarChartItem(
                        categorySpending = categorySpending,
                        totalSpending = uiState.totalSpending,
                        availableCategories = uiState.availableCategories,
                        index = index,
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )
                }

                item {
                    Spacer(modifier = Modifier.height(32.dp))
                }

            }
        }
    }
}


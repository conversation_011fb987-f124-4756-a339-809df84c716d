package org.example.dashboard

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import org.example.core.domain.model.Category
import kotlin.math.PI
import kotlin.math.atan2
import kotlin.math.min
import kotlin.math.sqrt

@Composable
fun DonutChart(
    totalSpending: Long,
    categorySpendings: List<CategorySpending>,
    availableCategories: List<Category>,
    modifier: Modifier = Modifier,
    innerRadiusRatio: Float = 0.4f
) {
    var selectedCategoryIndex by remember { mutableIntStateOf(-1) }

    // Fallback kolory dla kategorii, które nie mają zdefiniowanego koloru
    val fallbackColors = listOf(
        Color(0xFF6366F1), // Indigo
        Color(0xFF8B5CF6), // Violet
        Color(0xFFEC4899), // Pink
        Color(0xFFF59E0B), // Amber
        Color(0xFF10B981), // Emerald
        Color(0xFF3B82F6), // Blue
        Color(0xFFEF4444), // Red
        Color(0xFF84CC16), // Lime
        Color(0xFF06B6D4), // Cyan
        Color(0xFFF97316)  // Orange
    )

    // Funkcja do znalezienia koloru dla kategorii
    fun getCategoryColor(categoryName: String, fallbackIndex: Int): Color {
        val category = availableCategories.find { it.name == categoryName }
        return if (category != null) {
            Color(category.color)
        } else {
            fallbackColors[fallbackIndex % fallbackColors.size]
        }
    }

    // Obliczamy kąty dla każdej kategorii
    val angles = categorySpendings.map { categorySpending ->
        (categorySpending.categorySum.toFloat() / totalSpending.toFloat()) * 360f
    }

    // Funkcja do sprawdzenia czy punkt jest w danym łuku
    fun isPointInArc(
        offset: Offset,
        center: Offset,
        innerRadius: Float,
        outerRadius: Float,
        startAngle: Float,
        sweepAngle: Float
    ): Boolean {
        val dx = offset.x - center.x
        val dy = offset.y - center.y
        val distance = sqrt(dx * dx + dy * dy)

        if (distance < innerRadius || distance > outerRadius) return false

        var angle = (atan2(dy, dx) * 180 / PI + 360) % 360
        angle = (angle + 90) % 360 // Dostosowujemy do naszego układu współrzędnych

        val normalizedStartAngle = (startAngle + 360) % 360
        val endAngle = (normalizedStartAngle + sweepAngle) % 360

        return if (normalizedStartAngle <= endAngle) {
            angle >= normalizedStartAngle && angle <= endAngle
        } else {
            angle >= normalizedStartAngle || angle <= endAngle
        }
    }

    Box(
//        modifier = modifier.size(200.dp)
        modifier = modifier.size(150.dp),
//            .background(color = Color.Red.copy(alpha = 0.1f)),
        contentAlignment = Alignment.Center
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(Unit) {
                    detectTapGestures { offset ->
                        val canvasSize = min(size.width, size.height).toFloat()
                        val radius = canvasSize / 2
                        val innerRadius = radius * innerRadiusRatio
                        val center = Offset(size.width / 2f, size.height / 2f)

                        var currentStartAngle = -90f

                        categorySpendings.forEachIndexed { index, _ ->
                            val sweepAngle = angles[index]

                            if (isPointInArc(
                                    offset = offset,
                                    center = center,
                                    innerRadius = innerRadius,
                                    outerRadius = radius,
                                    startAngle = currentStartAngle,
                                    sweepAngle = sweepAngle
                                )
                            ) {
                                selectedCategoryIndex =
                                    if (selectedCategoryIndex == index) -1 else index
                                return@detectTapGestures
                            }

                            currentStartAngle += sweepAngle
                        }

                        // Kliknięcie poza łukami - reset
                        selectedCategoryIndex = -1
                    }
                }
        ) {
            val canvasSize = size.minDimension
            val radius = canvasSize / 2
            val innerRadius = radius * innerRadiusRatio
            val strokeWidth = radius - innerRadius

            var startAngle = -90f // Zaczynamy od góry

            categorySpendings.forEachIndexed { index, categorySpending ->
                val sweepAngle = angles[index]
                val color = getCategoryColor(categorySpending.categoryName, index)

                drawArc(
                    color = color,
                    startAngle = startAngle,
                    sweepAngle = sweepAngle,
                    useCenter = false,
                    style = Stroke(width = strokeWidth),
                    size = Size(canvasSize, canvasSize),
                    topLeft = Offset(
                        (size.width - canvasSize) / 2,
                        (size.height - canvasSize) / 2
                    )
                )

                startAngle += sweepAngle
            }
        }

        // Tekst w środku - pokazuje wybraną kategorię lub łączną kwotę
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (selectedCategoryIndex >= 0 && selectedCategoryIndex < categorySpendings.size) {
                val selectedCategory = categorySpendings[selectedCategoryIndex]
                Text(
                    text = selectedCategory.categoryName,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
                Text(
                    text = "${selectedCategory.categorySum / 100}zł",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
            } else {
                Text(
                    text = "Łącznie",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "${totalSpending / 100}zł",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    }
}
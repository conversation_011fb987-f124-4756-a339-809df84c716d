# Backlog

## ISSUES
- [ ] When entering AddReceipt with the flag “start scan” and killing the application process by the system (a separate issue with the garbage collector) while scanning mlkit document scanner, after restarting the application in a new process the image is recovered and loaded into view, but again the scanner is launched and has to be canceled.
- [ ] AddReceipt screen doesn't have save state handle.

## TO CHANGE
- [ ] Separate WeekProgressBar and DayProgressBar (DayProgressBar visible when a day is selected, at the bottom of component).
- [ ] CurrentWeekBudget.kt – change or remove additional info for adaptive budget.
- [x] (DONE in [Unreleased] - 2025-08-10) Toggle for PreviousWeekSummary – grid/bars + animations. Make PreviousWeekSummary to display grid or bars and add animations. Grid view should be 'normal', bar view should be compact.

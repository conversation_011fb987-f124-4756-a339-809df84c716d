package org.example.shared.component

import org.example.core.utils.DateUtils

/**
 * Represents different date filter options for receipts
 */
sealed class DateFilter(val displayName: String) {
    object All : DateFilter("All")
    object Today : DateFilter("Today")
    object ThisWeek : DateFilter("This week")
    object ThisMonth : DateFilter("This month")
    object Last7Days : DateFilter("Last 7 days")
    object LastMonth : DateFilter("Last month")
    data class Custom(val startDate: String?, val endDate: String?) : DateFilter("Custom")
    
    companion object {
        fun getDefaultFilters(): List<DateFilter> = listOf(
            All, Today, ThisWeek, ThisMonth, Last7Days, LastMonth, Custom(null, null)
        )
    }
}

/**
 * Represents a date range for filtering
 */
data class DateRange(
    val startDate: String?,
    val endDate: String?
)

/**
 * Utility functions for date filtering
 */
object DateFilterUtils {
    
    /**
     * Convert DateFilter to DateRange for database queries
     */
    fun getDateRange(filter: DateFilter): DateRange {
        val now = DateUtils.getCurrentTimestamp()

        return when (filter) {
            is DateFilter.All -> DateRange(null, null)

            is DateFilter.Today -> {
                val startOfDay = DateUtils.getStartOfDay(now)
                val endOfDay = DateUtils.getEndOfDay(now)
                DateRange(DateUtils.timestampToIsoString(startOfDay), DateUtils.timestampToIsoString(endOfDay))
            }
            
            is DateFilter.ThisWeek -> {
                val startOfWeek = DateUtils.getStartOfWeek(now)
                val endOfWeek = DateUtils.getEndOfWeek(now)
                DateRange(DateUtils.timestampToIsoString(startOfWeek), DateUtils.timestampToIsoString(endOfWeek))
            }

            is DateFilter.ThisMonth -> {
                val startOfMonth = DateUtils.getStartOfMonth(now)
                val endOfMonth = DateUtils.getEndOfMonth(now)
                DateRange(DateUtils.timestampToIsoString(startOfMonth), DateUtils.timestampToIsoString(endOfMonth))
            }

            is DateFilter.Last7Days -> {
                val sevenDaysAgo = now - (7 * 24 * 60 * 60 * 1000L) // 7 days in milliseconds
                DateRange(DateUtils.timestampToIsoString(sevenDaysAgo), DateUtils.timestampToIsoString(now))
            }
            
            is DateFilter.LastMonth -> {
                val currentMonthStart = DateUtils.getStartOfMonth(now)
                val lastMonthStart = DateUtils.getStartOfMonth(currentMonthStart - (24 * 60 * 60 * 1000L)) // Go back one day to get previous month
                val lastMonthEnd = currentMonthStart - 1 // End of last month
                DateRange(DateUtils.timestampToIsoString(lastMonthStart), DateUtils.timestampToIsoString(lastMonthEnd))
            }
            
            is DateFilter.Custom -> DateRange(filter.startDate, filter.endDate)
        }
    }
    

    
    /**
     * Format date range for display
     */
    fun formatDateRangeForDisplay(filter: DateFilter): String {
        return when (filter) {
            is DateFilter.All -> "All receipts"
            is DateFilter.Today -> "Today"
            is DateFilter.ThisWeek -> "This week"
            is DateFilter.ThisMonth -> "This month"
            is DateFilter.Last7Days -> "Last 7 days"
            is DateFilter.LastMonth -> "Last month"
            is DateFilter.Custom -> {
                when {
                    filter.startDate != null && filter.endDate != null -> {
                        "${formatDateShort(filter.startDate)} - ${formatDateShort(filter.endDate)}"
                    }
                    filter.startDate != null -> "From ${formatDateShort(filter.startDate)}"
                    filter.endDate != null -> "Until ${formatDateShort(filter.endDate)}"
                    else -> "Custom"
                }
            }
        }
    }
    
    /**
     * Format date string for short display (DD.MM)
     */
    private fun formatDateShort(dateString: String): String {
        return try {
            val timestamp = DateUtils.isoStringToTimestamp(dateString)
            DateUtils.timestampToShortDate(timestamp)
        } catch (e: Exception) {
            dateString
        }
    }
}

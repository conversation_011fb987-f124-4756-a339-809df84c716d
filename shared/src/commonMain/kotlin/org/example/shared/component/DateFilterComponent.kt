package org.example.shared.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CalendarToday
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import org.example.core.utils.DateUtils

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DateFilterComponent(
    currentFilter: DateFilter,
    isExpanded: Boolean,
    availableFilters: List<DateFilter>,
    onFilterSelected: (DateFilter) -> Unit,
    onExpandedChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    var showDatePicker by remember { mutableStateOf(false) }
    var isSelectingEndDate by remember { mutableStateOf(false) }
    var tempStartDate by remember { mutableStateOf<String?>(null) }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column {
            // Collapsed state - always visible
            CollapsedFilterRow(
                currentFilter = currentFilter,
                isExpanded = isExpanded,
                onExpandedChanged = onExpandedChanged,
                onPreviousFilter = {
                    val currentIndex = availableFilters.indexOf(currentFilter)
                    if (currentIndex > 0) {
                        onFilterSelected(availableFilters[currentIndex - 1])
                    }
                },
                onNextFilter = {
                    val currentIndex = availableFilters.indexOf(currentFilter)
                    if (currentIndex < availableFilters.size - 1) {
                        onFilterSelected(availableFilters[currentIndex + 1])
                    }
                }
            )
            
            // Expanded state - filter options
            AnimatedVisibility(
                visible = isExpanded,
                enter = expandVertically(),
                exit = shrinkVertically()
            ) {
                ExpandedFilterOptions(
                    availableFilters = availableFilters,
                    currentFilter = currentFilter,
                    onFilterSelected = { filter ->
                        if (filter is DateFilter.Custom) {
                            showDatePicker = true
                            isSelectingEndDate = false
                            tempStartDate = null
                        } else {
                            onFilterSelected(filter)
                        }
                    }
                )
            }
        }
    }
    
    // Date Picker Dialog
    if (showDatePicker) {
        val datePickerState = rememberDatePickerState()
        
        DatePickerDialog(
            onDismissRequest = { showDatePicker = false },
            confirmButton = {
                TextButton(
                    onClick = {
                        datePickerState.selectedDateMillis?.let { millis ->
                            val dateString = DateUtils.timestampToIsoString(millis)

                            if (!isSelectingEndDate) {
                                // First date selected - ask for end date
                                tempStartDate = dateString
                                isSelectingEndDate = true
                                // Reset picker for end date selection
                            } else {
                                // Second date selected - create custom filter
                                val customFilter = DateFilter.Custom(tempStartDate, dateString)
                                onFilterSelected(customFilter)
                                showDatePicker = false
                                isSelectingEndDate = false
                                tempStartDate = null
                            }
                        }
                    }
                ) {
                    Text(if (!isSelectingEndDate) "Select End Date" else "Confirm")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDatePicker = false }) {
                    Text("Cancel")
                }
            }
        ) {
            DatePicker(
                state = datePickerState,
                title = {
                    Text(
                        text = if (!isSelectingEndDate) "Select Start Date" else "Select End Date",
                        modifier = Modifier.padding(16.dp)
                    )
                }
            )
        }
    }
}

@Composable
private fun CollapsedFilterRow(
    currentFilter: DateFilter,
    isExpanded: Boolean,
    onExpandedChanged: (Boolean) -> Unit,
    onPreviousFilter: () -> Unit,
    onNextFilter: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onExpandedChanged(!isExpanded) }
            .padding(horizontal = 16.dp, vertical = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Left arrow
        IconButton(
            onClick = onPreviousFilter,
            modifier = Modifier.size(32.dp)
        ) {
            Icon(
                imageVector = Icons.Default.KeyboardArrowLeft,
                contentDescription = "Previous filter",
                tint = MaterialTheme.colorScheme.onSurface
            )
        }
        
        // Center content
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier.weight(1f)
        ) {
            Icon(
                imageVector = Icons.Default.CalendarToday,
                contentDescription = "Calendar",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = DateFilterUtils.formatDateRangeForDisplay(currentFilter),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Icon(
                imageVector = if (isExpanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                contentDescription = if (isExpanded) "Collapse" else "Expand",
                tint = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.size(20.dp)
            )
        }
        
        // Right arrow
        IconButton(
            onClick = onNextFilter,
            modifier = Modifier.size(32.dp)
        ) {
            Icon(
                imageVector = Icons.Default.KeyboardArrowRight,
                contentDescription = "Next filter",
                tint = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

@Composable
private fun ExpandedFilterOptions(
    availableFilters: List<DateFilter>,
    currentFilter: DateFilter,
    onFilterSelected: (DateFilter) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        availableFilters.forEach { filter ->
            FilterOptionItem(
                filter = filter,
                isSelected = filter::class == currentFilter::class,
                onSelected = { onFilterSelected(filter) }
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
    }
}

@Composable
private fun FilterOptionItem(
    filter: DateFilter,
    isSelected: Boolean,
    onSelected: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(
                if (isSelected) MaterialTheme.colorScheme.primaryContainer
                else Color.Transparent
            )
            .clickable { onSelected() }
            .padding(horizontal = 16.dp, vertical = 12.dp)
    ) {
        Text(
            text = DateFilterUtils.formatDateRangeForDisplay(filter),
            style = MaterialTheme.typography.bodyLarge,
            color = if (isSelected) MaterialTheme.colorScheme.onPrimaryContainer
                   else MaterialTheme.colorScheme.onSurface,
            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
        )
    }
}

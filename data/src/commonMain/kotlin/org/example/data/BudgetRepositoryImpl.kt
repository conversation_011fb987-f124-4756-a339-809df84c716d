package org.example.data

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.datetime.LocalDate
import org.example.core.domain.model.Budget
import org.example.core.domain.model.BudgetChangeType
import org.example.core.domain.model.BudgetHistory
import org.example.core.domain.repository.BudgetRepository
import org.example.data.database.AppDatabase
import org.example.data.database.entity.BudgetEntity
import org.example.data.database.entity.BudgetHistoryEntity

/**
 * Room database implementation of BudgetRepository
 */
class BudgetRepositoryImpl(
    private val database: AppDatabase
) : BudgetRepository {
    private val budgetDao = database.budgetDao()

    override suspend fun getBudgetForWeek(weekStartDate: LocalDate): Budget? {
        val timestamp = weekStartDate.toEpochDays() * 24 * 60 * 60 * 1000L
        return budgetDao.getBudgetByWeekStartDate(timestamp)?.toDomainModel()
    }

    override suspend fun getBudgetForWeek(year: Int, weekNumber: Int): Budget? {
        return budgetDao.getBudgetByYearAndWeek(year, weekNumber)?.toDomainModel()
    }

    override fun getAllBudgets(): Flow<List<Budget>> {
        return budgetDao.getAllBudgets().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getBudgetsForYear(year: Int): Flow<List<Budget>> {
        return budgetDao.getBudgetsForYear(year).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override suspend fun saveBudget(budget: Budget): Budget {
        val entity = BudgetEntity.fromDomainModel(budget)
        val historyEntry = createHistoryEntry(budget, null, BudgetChangeType.CREATED)
        
        val budgetId = budgetDao.insertBudgetWithHistory(entity, historyEntry)
        return budget.copy(id = budgetId)
    }

    override suspend fun updateBudget(budget: Budget, changeReason: String?): Budget {
        // Get the existing budget to track previous amount
        val existingBudget = budgetDao.getBudgetById(budget.id)
        val previousAmount = existingBudget?.budgetAmountInCents
        
        val entity = BudgetEntity.fromDomainModel(budget)
        val historyEntry = createHistoryEntry(budget, previousAmount, BudgetChangeType.UPDATED, changeReason)
        
        budgetDao.updateBudgetWithHistory(entity, historyEntry)
        return budget
    }

    override suspend fun deleteBudget(budgetId: Long, changeReason: String?) {
        val existingBudget = budgetDao.getBudgetById(budgetId)
        if (existingBudget != null) {
            val budget = existingBudget.toDomainModel()
            val historyEntry = createHistoryEntry(
                budget, 
                budget.budgetAmountInCents, 
                BudgetChangeType.DELETED, 
                changeReason
            )
            
            budgetDao.deleteBudgetWithHistory(budgetId, historyEntry)
        }
    }

    override fun getBudgetHistory(budgetId: Long): Flow<List<BudgetHistory>> {
        return budgetDao.getBudgetHistory(budgetId).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getBudgetHistoryForWeek(weekStartDate: LocalDate): Flow<List<BudgetHistory>> {
        val timestamp = weekStartDate.toEpochDays() * 24 * 60 * 60 * 1000L
        return budgetDao.getBudgetHistoryByWeekStartDate(timestamp).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getBudgetHistoryForWeek(year: Int, weekNumber: Int): Flow<List<BudgetHistory>> {
        return budgetDao.getBudgetHistoryByYearAndWeek(year, weekNumber).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getAllBudgetHistory(): Flow<List<BudgetHistory>> {
        return budgetDao.getAllBudgetHistory().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    private fun createHistoryEntry(
        budget: Budget,
        previousAmount: Long?,
        changeType: BudgetChangeType,
        changeReason: String? = null
    ): BudgetHistoryEntity {
        val currentTime = kotlinx.datetime.Clock.System.now().toEpochMilliseconds()
        
        val historyEntry = BudgetHistory(
            budgetId = budget.id,
            weekStartDate = budget.weekStartDate,
            weekEndDate = budget.weekEndDate,
            weekNumber = budget.weekNumber,
            year = budget.year,
            previousAmountInCents = previousAmount,
            newAmountInCents = budget.budgetAmountInCents,
            changeType = changeType,
            changeReason = changeReason,
            createdAt = currentTime
        )
        
        return BudgetHistoryEntity.fromDomainModel(historyEntry)
    }
}

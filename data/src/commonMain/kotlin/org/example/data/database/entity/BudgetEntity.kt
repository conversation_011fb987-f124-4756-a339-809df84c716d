package org.example.data.database.entity

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDate
import org.example.core.domain.model.Budget

/**
 * Room entity for Budget data
 * Stores weekly budget information
 */
@Entity(
    tableName = "budgets",
    indices = [
        Index(value = ["weekStartDateTimestamp"], unique = true),
        Index(value = ["year", "weekNumber"], unique = true)
    ]
)
data class BudgetEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val weekStartDateTimestamp: Long,
    val weekEndDateTimestamp: Long,
    val weekNumber: Int,
    val year: Int,
    val budgetAmountInCents: Long,
    val createdAt: Long,
    val updatedAt: Long
) {
    /**
     * Convert entity to domain model
     */
    fun toDomainModel(): Budget {
        return Budget(
            id = id,
            weekStartDate = LocalDate.fromEpochDays((weekStartDateTimestamp / (24 * 60 * 60 * 1000)).toInt()),
            weekEndDate = LocalDate.fromEpochDays((weekEndDateTimestamp / (24 * 60 * 60 * 1000)).toInt()),
            weekNumber = weekNumber,
            year = year,
            budgetAmountInCents = budgetAmountInCents,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
    
    companion object {
        /**
         * Convert domain model to entity
         */
        fun fromDomainModel(budget: Budget): BudgetEntity {
            return BudgetEntity(
                id = budget.id,
                weekStartDateTimestamp = budget.weekStartDate.toEpochDays() * 24 * 60 * 60 * 1000L,
                weekEndDateTimestamp = budget.weekEndDate.toEpochDays() * 24 * 60 * 60 * 1000L,
                weekNumber = budget.weekNumber,
                year = budget.year,
                budgetAmountInCents = budget.budgetAmountInCents,
                createdAt = budget.createdAt,
                updatedAt = budget.updatedAt
            )
        }
    }
}

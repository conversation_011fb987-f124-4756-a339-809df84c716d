package org.example.data.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import kotlinx.coroutines.flow.Flow
import org.example.data.database.entity.BudgetEntity
import org.example.data.database.entity.BudgetHistoryEntity

/**
 * Data Access Object for Budget operations
 */
@Dao
interface BudgetDao {
    
    /**
     * Insert a new budget
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBudget(budget: BudgetEntity): Long
    
    /**
     * Update an existing budget
     */
    @Update
    suspend fun updateBudget(budget: BudgetEntity)
    
    /**
     * Delete a budget by ID
     */
    @Query("DELETE FROM budgets WHERE id = :budgetId")
    suspend fun deleteBudget(budgetId: Long)
    
    /**
     * Get budget by week start date timestamp
     */
    @Query("SELECT * FROM budgets WHERE weekStartDateTimestamp = :weekStartDateTimestamp")
    suspend fun getBudgetByWeekStartDate(weekStartDateTimestamp: Long): BudgetEntity?
    
    /**
     * Get budget by year and week number
     */
    @Query("SELECT * FROM budgets WHERE year = :year AND weekNumber = :weekNumber")
    suspend fun getBudgetByYearAndWeek(year: Int, weekNumber: Int): BudgetEntity?
    
    /**
     * Get budget by ID
     */
    @Query("SELECT * FROM budgets WHERE id = :budgetId")
    suspend fun getBudgetById(budgetId: Long): BudgetEntity?
    
    /**
     * Get all budgets ordered by week start date
     */
    @Query("SELECT * FROM budgets ORDER BY weekStartDateTimestamp ASC")
    fun getAllBudgets(): Flow<List<BudgetEntity>>
    
    /**
     * Get budgets for a specific year
     */
    @Query("SELECT * FROM budgets WHERE year = :year ORDER BY weekNumber ASC")
    fun getBudgetsForYear(year: Int): Flow<List<BudgetEntity>>
    
    /**
     * Insert budget history entry
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBudgetHistory(budgetHistory: BudgetHistoryEntity): Long
    
    /**
     * Get budget history for a specific budget
     */
    @Query("SELECT * FROM budget_history WHERE budgetId = :budgetId ORDER BY createdAt DESC")
    fun getBudgetHistory(budgetId: Long): Flow<List<BudgetHistoryEntity>>
    
    /**
     * Get budget history for a specific week by start date
     */
    @Query("SELECT * FROM budget_history WHERE weekStartDateTimestamp = :weekStartDateTimestamp ORDER BY createdAt DESC")
    fun getBudgetHistoryByWeekStartDate(weekStartDateTimestamp: Long): Flow<List<BudgetHistoryEntity>>
    
    /**
     * Get budget history for a specific week by year and week number
     */
    @Query("SELECT * FROM budget_history WHERE year = :year AND weekNumber = :weekNumber ORDER BY createdAt DESC")
    fun getBudgetHistoryByYearAndWeek(year: Int, weekNumber: Int): Flow<List<BudgetHistoryEntity>>
    
    /**
     * Get all budget history entries
     */
    @Query("SELECT * FROM budget_history ORDER BY createdAt DESC")
    fun getAllBudgetHistory(): Flow<List<BudgetHistoryEntity>>
    
    /**
     * Transaction to update budget and create history entry
     */
    @Transaction
    suspend fun updateBudgetWithHistory(
        budget: BudgetEntity,
        historyEntry: BudgetHistoryEntity
    ) {
        updateBudget(budget)
        insertBudgetHistory(historyEntry)
    }
    
    /**
     * Transaction to insert budget and create history entry
     */
    @Transaction
    suspend fun insertBudgetWithHistory(
        budget: BudgetEntity,
        historyEntry: BudgetHistoryEntity
    ): Long {
        val budgetId = insertBudget(budget)
        val historyWithBudgetId = historyEntry.copy(budgetId = budgetId)
        insertBudgetHistory(historyWithBudgetId)
        return budgetId
    }
    
    /**
     * Transaction to delete budget and create history entry
     */
    @Transaction
    suspend fun deleteBudgetWithHistory(
        budgetId: Long,
        historyEntry: BudgetHistoryEntity
    ) {
        insertBudgetHistory(historyEntry)
        deleteBudget(budgetId)
    }
}

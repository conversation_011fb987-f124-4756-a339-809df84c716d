package org.example.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import org.example.data.database.entity.MyTextBlockEntity

/**
 * Data Access Object for MyTextBlock operations
 */
@Dao
interface MyTextBlockDao {
    
    /**
     * Insert a new text block
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTextBlock(textBlock: MyTextBlockEntity): Long
    
    /**
     * Insert multiple text blocks
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTextBlocks(textBlocks: List<MyTextBlockEntity>): List<Long>
    
    /**
     * Get a text block by ID
     */
    @Query("SELECT * FROM text_blocks WHERE id = :id")
    suspend fun getTextBlockById(id: Long): MyTextBlockEntity?
    
    /**
     * Get all text blocks for a specific grouped text line
     */
    @Query("SELECT * FROM text_blocks WHERE groupedTextLineId = :groupedTextLineId ORDER BY id ASC")
    suspend fun getTextBlocksByGroupedTextLineId(groupedTextLineId: Long): List<MyTextBlockEntity>
    
    /**
     * Get text blocks by bounding box ID
     */
    @Query("SELECT * FROM text_blocks WHERE boundingBoxId = :boundingBoxId")
    suspend fun getTextBlocksByBoundingBoxId(boundingBoxId: Long): List<MyTextBlockEntity>
    
    /**
     * Update a text block
     */
    @Update
    suspend fun updateTextBlock(textBlock: MyTextBlockEntity)
    
    /**
     * Delete a text block by ID
     */
    @Query("DELETE FROM text_blocks WHERE id = :id")
    suspend fun deleteTextBlockById(id: Long)
    
    /**
     * Delete text blocks by grouped text line ID
     */
    @Query("DELETE FROM text_blocks WHERE groupedTextLineId = :groupedTextLineId")
    suspend fun deleteTextBlocksByGroupedTextLineId(groupedTextLineId: Long)
    
    /**
     * Delete a text block entity
     */
    @Delete
    suspend fun deleteTextBlock(textBlock: MyTextBlockEntity)
    
    /**
     * Delete all text blocks
     */
    @Query("DELETE FROM text_blocks")
    suspend fun deleteAllTextBlocks()
}

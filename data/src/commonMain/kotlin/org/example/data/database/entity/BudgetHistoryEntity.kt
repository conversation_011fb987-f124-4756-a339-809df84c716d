package org.example.data.database.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDate
import org.example.core.domain.model.BudgetChangeType
import org.example.core.domain.model.BudgetHistory

/**
 * Room entity for BudgetHistory data
 * Stores budget change history information
 */
@Entity(
    tableName = "budget_history",
    foreignKeys = [
        ForeignKey(
            entity = BudgetEntity::class,
            parentColumns = ["id"],
            childColumns = ["budgetId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["budgetId"]),
        Index(value = ["weekStartDateTimestamp"]),
        Index(value = ["year", "weekNumber"]),
        Index(value = ["createdAt"])
    ]
)
data class BudgetHistoryEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val budgetId: Long,
    val weekStartDateTimestamp: Long,
    val weekEndDateTimestamp: Long,
    val weekNumber: Int,
    val year: Int,
    val previousAmountInCents: Long?,
    val newAmountInCents: Long,
    val changeType: String, // BudgetChangeType as string
    val changeReason: String?,
    val createdAt: Long
) {
    /**
     * Convert entity to domain model
     */
    fun toDomainModel(): BudgetHistory {
        return BudgetHistory(
            id = id,
            budgetId = budgetId,
            weekStartDate = LocalDate.fromEpochDays((weekStartDateTimestamp / (24 * 60 * 60 * 1000)).toInt()),
            weekEndDate = LocalDate.fromEpochDays((weekEndDateTimestamp / (24 * 60 * 60 * 1000)).toInt()),
            weekNumber = weekNumber,
            year = year,
            previousAmountInCents = previousAmountInCents,
            newAmountInCents = newAmountInCents,
            changeType = BudgetChangeType.valueOf(changeType),
            changeReason = changeReason,
            createdAt = createdAt
        )
    }
    
    companion object {
        /**
         * Convert domain model to entity
         */
        fun fromDomainModel(budgetHistory: BudgetHistory): BudgetHistoryEntity {
            return BudgetHistoryEntity(
                id = budgetHistory.id,
                budgetId = budgetHistory.budgetId,
                weekStartDateTimestamp = budgetHistory.weekStartDate.toEpochDays() * 24 * 60 * 60 * 1000L,
                weekEndDateTimestamp = budgetHistory.weekEndDate.toEpochDays() * 24 * 60 * 60 * 1000L,
                weekNumber = budgetHistory.weekNumber,
                year = budgetHistory.year,
                previousAmountInCents = budgetHistory.previousAmountInCents,
                newAmountInCents = budgetHistory.newAmountInCents,
                changeType = budgetHistory.changeType.name,
                changeReason = budgetHistory.changeReason,
                createdAt = budgetHistory.createdAt
            )
        }
    }
}

package org.example.data.database

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import org.example.data.database.dao.BudgetDao
import org.example.data.database.dao.CategoryDao
import org.example.data.database.dao.GroupedTextLineDao
import org.example.data.database.dao.MyBoundingBoxDao
import org.example.data.database.dao.MyTextBlockDao
import org.example.data.database.dao.ProductDao
import org.example.data.database.dao.ReceiptDao
import org.example.data.database.dao.TypeDao
import org.example.data.database.entity.BudgetEntity
import org.example.data.database.entity.BudgetHistoryEntity
import org.example.data.database.entity.CategoryEntity
import org.example.data.database.entity.GroupedTextLineEntity
import org.example.data.database.entity.MyBoundingBoxEntity
import org.example.data.database.entity.MyTextBlockEntity
import org.example.data.database.entity.ProductEntity
import org.example.data.database.entity.ReceiptEntity
import org.example.data.database.entity.TypeEntity

/**
 * Room database for the application
 */
@Database(
    entities = [
        CategoryEntity::class,
        TypeEntity::class,
        ReceiptEntity::class,
        ProductEntity::class,
        MyBoundingBoxEntity::class,
        MyTextBlockEntity::class,
        GroupedTextLineEntity::class,
        BudgetEntity::class,
        BudgetHistoryEntity::class
    ],
    version = 5,
    exportSchema = false
)
@TypeConverters(org.example.data.database.TypeConverters::class)
abstract class AppDatabase : RoomDatabase() {
    
    abstract fun categoryDao(): CategoryDao
    abstract fun typeDao(): TypeDao
    abstract fun receiptDao(): ReceiptDao
    abstract fun productDao(): ProductDao
    abstract fun myBoundingBoxDao(): MyBoundingBoxDao
    abstract fun myTextBlockDao(): MyTextBlockDao
    abstract fun groupedTextLineDao(): GroupedTextLineDao
    abstract fun budgetDao(): BudgetDao
    
    companion object {
        const val DATABASE_NAME = "scan_receipt_database"
    }
}

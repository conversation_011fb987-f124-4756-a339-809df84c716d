package org.example.data.database.service

import org.example.core.domain.model.GroupedTextLine
import org.example.data.database.AppDatabase
import org.example.data.database.entity.GroupedTextLineEntity
import org.example.data.database.entity.MyBoundingBoxEntity
import org.example.data.database.entity.MyTextBlockEntity

/**
 * Service class to handle complex highlighting data operations
 * Manages the relationships between GroupedTextLine, MyBoundingBox, and MyTextBlock entities
 */
class HighlightingService(private val database: AppDatabase) {

    private val boundingBoxDao = database.myBoundingBoxDao()
    private val textBlockDao = database.myTextBlockDao()
    private val groupedTextLineDao = database.groupedTextLineDao()

    /**
     * Save a GroupedTextLine with all its related data
     * @param groupedTextLine The GroupedTextLine to save
     * @param productId The ID of the product this highlighting belongs to
     * @return The ID of the saved GroupedTextLineEntity
     */
    suspend fun saveGroupedTextLine(groupedTextLine: GroupedTextLine, productId: String): Long {
        println("HS productId: $productId")
        println("HS saveGroupedTextLine: $groupedTextLine")
        // 1. Save the main bounding box
        val mainBoundingBoxEntity =
            MyBoundingBoxEntity.fromDomainModel(groupedTextLine.myBoundingBox)
        val mainBoundingBoxId = boundingBoxDao.insertBoundingBox(mainBoundingBoxEntity)

        // 2. Save the GroupedTextLine entity
        val groupedTextLineEntity = GroupedTextLineEntity.fromDomainModel(
            groupedTextLine,
            mainBoundingBoxId,
            productId
        )
        val groupedTextLineId = groupedTextLineDao.insertGroupedTextLine(groupedTextLineEntity)

        // 3. Save all text block elements
        groupedTextLine.elements.forEach { textBlock ->
            // Save bounding box for each text block
            val textBlockBoundingBoxEntity =
                MyBoundingBoxEntity.fromDomainModel(textBlock.myBoundingBox)
            val textBlockBoundingBoxId =
                boundingBoxDao.insertBoundingBox(textBlockBoundingBoxEntity)

            // Save text block
            val textBlockEntity = MyTextBlockEntity.fromDomainModel(
                textBlock,
                textBlockBoundingBoxId,
                groupedTextLineId
            )
            textBlockDao.insertTextBlock(textBlockEntity)
        }

        return groupedTextLineId
    }

    /**
     * Load a GroupedTextLine by product ID
     * @param productId The ID of the product
     * @return The GroupedTextLine or null if not found
     */
    suspend fun getGroupedTextLineByProductId(productId: String): GroupedTextLine? {
        // 1. Get the GroupedTextLineEntity
        val groupedTextLineEntity = groupedTextLineDao.getGroupedTextLineByProductId(productId)
            ?: return null

        // 2. Get the main bounding box
        val mainBoundingBox = boundingBoxDao.getBoundingBoxById(groupedTextLineEntity.boundingBoxId)
            ?.toDomainModel() ?: return null

        // 3. Get all text block elements
        val textBlockEntities =
            textBlockDao.getTextBlocksByGroupedTextLineId(groupedTextLineEntity.id)

        // 4. Load bounding boxes for text blocks
        val textBlockBoundingBoxIds = textBlockEntities.map { it.boundingBoxId }
        val textBlockBoundingBoxes = boundingBoxDao.getBoundingBoxesByIds(textBlockBoundingBoxIds)
            .associateBy { it.id }

        // 5. Convert text blocks to domain models
        val textBlocks = textBlockEntities.mapNotNull { textBlockEntity ->
            val boundingBox = textBlockBoundingBoxes[textBlockEntity.boundingBoxId]?.toDomainModel()
            if (boundingBox != null) {
                textBlockEntity.toDomainModel(boundingBox)
            } else {
                null
            }
        }

        // 6. Create and return the GroupedTextLine
        return groupedTextLineEntity.toDomainModel(mainBoundingBox, textBlocks)
    }

    /**
     * Load multiple GroupedTextLines by product IDs
     * @param productIds List of product IDs
     * @return Map of productId to GroupedTextLine
     */
    suspend fun getGroupedTextLinesByProductIds(productIds: List<String>): Map<String, GroupedTextLine> {
        if (productIds.isEmpty()) return emptyMap()

        val result = mutableMapOf<String, GroupedTextLine>()

        // Get all GroupedTextLineEntities for the products
        val groupedTextLineEntities = groupedTextLineDao.getGroupedTextLinesByProductIds(productIds)

        if (groupedTextLineEntities.isEmpty()) return emptyMap()

        // Get all main bounding boxes
        val mainBoundingBoxIds = groupedTextLineEntities.map { it.boundingBoxId }
        val mainBoundingBoxes = boundingBoxDao.getBoundingBoxesByIds(mainBoundingBoxIds)
            .associateBy { it.id }

        // Process each GroupedTextLine
        for (groupedTextLineEntity in groupedTextLineEntities) {
            val mainBoundingBox =
                mainBoundingBoxes[groupedTextLineEntity.boundingBoxId]?.toDomainModel()
            if (mainBoundingBox == null) continue

            // Get text blocks for this grouped text line
            val textBlockEntities =
                textBlockDao.getTextBlocksByGroupedTextLineId(groupedTextLineEntity.id)

            // Get bounding boxes for text blocks
            val textBlockBoundingBoxIds = textBlockEntities.map { it.boundingBoxId }
            val textBlockBoundingBoxes = if (textBlockBoundingBoxIds.isNotEmpty()) {
                boundingBoxDao.getBoundingBoxesByIds(textBlockBoundingBoxIds).associateBy { it.id }
            } else {
                emptyMap()
            }

            // Convert text blocks to domain models
            val textBlocks = textBlockEntities.mapNotNull { textBlockEntity ->
                val boundingBox =
                    textBlockBoundingBoxes[textBlockEntity.boundingBoxId]?.toDomainModel()
                if (boundingBox != null) {
                    textBlockEntity.toDomainModel(boundingBox)
                } else {
                    null
                }
            }

            // Create GroupedTextLine and add to result
            val groupedTextLine = groupedTextLineEntity.toDomainModel(mainBoundingBox, textBlocks)
            result[groupedTextLineEntity.productId] = groupedTextLine
        }

        return result
    }

    /**
     * Delete highlighting data for a product
     * @param productId The ID of the product
     */
    suspend fun deleteGroupedTextLineByProductId(productId: String) {
        groupedTextLineDao.deleteGroupedTextLineByProductId(productId)
        // Note: Bounding boxes and text blocks will be deleted automatically due to CASCADE foreign keys
    }

    /**
     * Delete highlighting data for multiple products
     * @param productIds List of product IDs
     */
    suspend fun deleteGroupedTextLinesByProductIds(productIds: List<String>) {
        productIds.forEach { productId ->
            deleteGroupedTextLineByProductId(productId)
        }
    }
}

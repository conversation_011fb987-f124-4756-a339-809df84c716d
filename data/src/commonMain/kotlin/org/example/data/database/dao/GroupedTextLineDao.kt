package org.example.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import org.example.data.database.entity.GroupedTextLineEntity

/**
 * Data Access Object for GroupedTextLine operations
 */
@Dao
interface GroupedTextLineDao {
    
    /**
     * Insert a new grouped text line
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGroupedTextLine(groupedTextLine: GroupedTextLineEntity): Long
    
    /**
     * Insert multiple grouped text lines
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGroupedTextLines(groupedTextLines: List<GroupedTextLineEntity>): List<Long>
    
    /**
     * Get a grouped text line by ID
     */
    @Query("SELECT * FROM grouped_text_lines WHERE id = :id")
    suspend fun getGroupedTextLineById(id: Long): GroupedTextLineEntity?
    
    /**
     * Get grouped text line by product ID
     */
    @Query("SELECT * FROM grouped_text_lines WHERE productId = :productId")
    suspend fun getGroupedTextLineByProductId(productId: String): GroupedTextLineEntity?
    
    /**
     * Get all grouped text lines for multiple product IDs
     */
    @Query("SELECT * FROM grouped_text_lines WHERE productId IN (:productIds)")
    suspend fun getGroupedTextLinesByProductIds(productIds: List<String>): List<GroupedTextLineEntity>
    
    /**
     * Get grouped text line by bounding box ID
     */
    @Query("SELECT * FROM grouped_text_lines WHERE boundingBoxId = :boundingBoxId")
    suspend fun getGroupedTextLineByBoundingBoxId(boundingBoxId: Long): GroupedTextLineEntity?
    
    /**
     * Update a grouped text line
     */
    @Update
    suspend fun updateGroupedTextLine(groupedTextLine: GroupedTextLineEntity)
    
    /**
     * Delete a grouped text line by ID
     */
    @Query("DELETE FROM grouped_text_lines WHERE id = :id")
    suspend fun deleteGroupedTextLineById(id: Long)
    
    /**
     * Delete grouped text line by product ID
     */
    @Query("DELETE FROM grouped_text_lines WHERE productId = :productId")
    suspend fun deleteGroupedTextLineByProductId(productId: String)
    
    /**
     * Delete a grouped text line entity
     */
    @Delete
    suspend fun deleteGroupedTextLine(groupedTextLine: GroupedTextLineEntity)
    
    /**
     * Delete all grouped text lines
     */
    @Query("DELETE FROM grouped_text_lines")
    suspend fun deleteAllGroupedTextLines()
}

package org.example.data.database.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import org.example.core.domain.model.GroupedTextLine
import org.example.core.domain.model.MyBoundingBox
import org.example.core.domain.model.MyTextBlock

/**
 * Room entity for GroupedTextLine data
 * Stores text line information for product highlighting
 */
@Entity(
    tableName = "grouped_text_lines",
    foreignKeys = [
        ForeignKey(
            entity = MyBoundingBoxEntity::class,
            parentColumns = ["id"],
            childColumns = ["boundingBoxId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = ProductEntity::class,
            parentColumns = ["id"],
            childColumns = ["productId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["boundingBoxId"]),
        Index(value = ["productId"])
    ]
)
data class GroupedTextLineEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val text: String,
    val boundingBoxId: Long,
    val productId: String
) {
    /**
     * Convert entity to domain model
     * Note: MyBoundingBox and elements will be loaded separately via relationships
     */
    fun toDomainModel(
        boundingBox: MyBoundingBox,
        elements: List<MyTextBlock>
    ): GroupedTextLine {
        return GroupedTextLine(
            text = text,
            myBoundingBox = boundingBox,
            elements = elements
        )
    }
    
    companion object {
        /**
         * Convert domain model to entity
         * Note: boundingBoxId and productId must be provided separately
         */
        fun fromDomainModel(
            groupedTextLine: GroupedTextLine, 
            boundingBoxId: Long, 
            productId: String
        ): GroupedTextLineEntity {
            return GroupedTextLineEntity(
                text = groupedTextLine.text,
                boundingBoxId = boundingBoxId,
                productId = productId
            )
        }
    }
}

package org.example.data.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import org.example.core.domain.model.MyBoundingBox

/**
 * Room entity for MyBoundingBox data
 * Stores coordinate information for text highlighting
 */
@Entity(tableName = "bounding_boxes")
data class MyBoundingBoxEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val left: Float,
    val top: Float,
    val right: Float,
    val bottom: Float
) {
    /**
     * Convert entity to domain model
     */
    fun toDomainModel(): MyBoundingBox {
        return MyBoundingBox(
            left = left,
            top = top,
            right = right,
            bottom = bottom
        )
    }
    
    companion object {
        /**
         * Convert domain model to entity
         */
        fun fromDomainModel(boundingBox: MyBoundingBox): MyBoundingBoxEntity {
            return MyBoundingBoxEntity(
                left = boundingBox.left,
                top = boundingBox.top,
                right = boundingBox.right,
                bottom = boundingBox.bottom
            )
        }
    }
}

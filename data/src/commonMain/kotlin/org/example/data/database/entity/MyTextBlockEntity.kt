package org.example.data.database.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import org.example.core.domain.model.MyBoundingBox
import org.example.core.domain.model.MyTextBlock

/**
 * Room entity for MyTextBlock data
 * Stores individual text elements with their bounding boxes
 */
@Entity(
    tableName = "text_blocks",
    foreignKeys = [
        ForeignKey(
            entity = MyBoundingBoxEntity::class,
            parentColumns = ["id"],
            childColumns = ["boundingBoxId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = GroupedTextLineEntity::class,
            parentColumns = ["id"],
            childColumns = ["groupedTextLineId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["boundingBoxId"]),
        Index(value = ["groupedTextLineId"])
    ]
)
data class MyTextBlockEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val text: String,
    val boundingBoxId: Long,
    val confidence: Float = 1f,
    val groupedTextLineId: Long
) {
    /**
     * Convert entity to domain model
     * Note: MyBoundingBox will be loaded separately via relationship
     */
    fun toDomainModel(boundingBox: MyBoundingBox): MyTextBlock {
        return MyTextBlock(
            text = text,
            myBoundingBox = boundingBox,
            confidence = confidence
        )
    }
    
    companion object {
        /**
         * Convert domain model to entity
         * Note: boundingBoxId and groupedTextLineId must be provided separately
         */
        fun fromDomainModel(
            textBlock: MyTextBlock, 
            boundingBoxId: Long, 
            groupedTextLineId: Long
        ): MyTextBlockEntity {
            return MyTextBlockEntity(
                text = textBlock.text,
                boundingBoxId = boundingBoxId,
                confidence = textBlock.confidence,
                groupedTextLineId = groupedTextLineId
            )
        }
    }
}

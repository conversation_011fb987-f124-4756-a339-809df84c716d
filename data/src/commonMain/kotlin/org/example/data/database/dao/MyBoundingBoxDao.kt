package org.example.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import org.example.data.database.entity.MyBoundingBoxEntity

/**
 * Data Access Object for MyBoundingBox operations
 */
@Dao
interface MyBoundingBoxDao {
    
    /**
     * Insert a new bounding box
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBoundingBox(boundingBox: MyBoundingBoxEntity): Long
    
    /**
     * Insert multiple bounding boxes
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBoundingBoxes(boundingBoxes: List<MyBoundingBoxEntity>): List<Long>
    
    /**
     * Get a bounding box by ID
     */
    @Query("SELECT * FROM bounding_boxes WHERE id = :id")
    suspend fun getBoundingBoxById(id: Long): MyBoundingBoxEntity?
    
    /**
     * Get multiple bounding boxes by IDs
     */
    @Query("SELECT * FROM bounding_boxes WHERE id IN (:ids)")
    suspend fun getBoundingBoxesByIds(ids: List<Long>): List<MyBoundingBoxEntity>
    
    /**
     * Update a bounding box
     */
    @Update
    suspend fun updateBoundingBox(boundingBox: MyBoundingBoxEntity)
    
    /**
     * Delete a bounding box by ID
     */
    @Query("DELETE FROM bounding_boxes WHERE id = :id")
    suspend fun deleteBoundingBoxById(id: Long)
    
    /**
     * Delete a bounding box entity
     */
    @Delete
    suspend fun deleteBoundingBox(boundingBox: MyBoundingBoxEntity)
    
    /**
     * Delete all bounding boxes
     */
    @Query("DELETE FROM bounding_boxes")
    suspend fun deleteAllBoundingBoxes()
}

rootProject.name = "ScanReceipt"
enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")

pluginManagement {
    repositories {
        google {
            mavenContent {
                includeGroupAndSubgroups("androidx")
                includeGroupAndSubgroups("com.android")
                includeGroupAndSubgroups("com.google")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositories {
        google {
            mavenContent {
                includeGroupAndSubgroups("androidx")
                includeGroupAndSubgroups("com.android")
                includeGroupAndSubgroups("com.google")
            }
        }
        mavenCentral()
    }
}
include(":core")
include(":data")
include(":di")
include(":feature:budget")

include(":feature:dashboard")
include(":feature:categories")
include(":feature:listreceipt")
include(":feature:receiptlist")
include(":feature:addreceipt")
include("feature:home")
include(":navigation")
include(":composeApp")
include(":shared")
